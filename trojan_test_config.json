{"log": {"level": "info", "timestamp": true}, "dns": {"servers": [{"tag": "google", "address": "*******"}, {"tag": "local", "address": "local", "detour": "direct"}], "rules": [{"outbound": "any", "server": "local"}]}, "inbounds": [{"type": "mixed", "tag": "mixed-in", "listen": "127.0.0.1", "listen_port": 2080}], "outbounds": [{"type": "trojan", "tag": "trojan-tcp", "server": "ductuspro.ru", "server_port": 443, "password": "15c175d8-703c-456a-ac82-91041f8af845", "tls": {"enabled": true, "server_name": "ductuspro.ru", "insecure": false}}, {"type": "trojan", "tag": "trojan-ws", "server": "ductuspro.ru", "server_port": 443, "password": "15c175d8-703c-456a-ac82-91041f8af845", "transport": {"type": "ws", "path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx"}, "tls": {"enabled": true, "server_name": "ductuspro.ru", "insecure": false}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}], "route": {"rules": [{"ip_is_private": true, "outbound": "direct"}], "final": "trojan-tcp", "auto_detect_interface": true}}