#!/usr/bin/env python3
"""
Тестирование упрощенного административного endpoint с API ключом.
"""

import requests
import json
import time
from datetime import datetime

# Конфигурация
BASE_URL = "http://ductuspro.ru:8090"
API_KEY = "be84eb6e-cf9d-4b2b-b063-fdf26960ebca"

def test_simple_endpoint():
    """Тестирует упрощенный административный endpoint."""
    
    print("=" * 80)
    print("ТЕСТИРОВАНИЕ УПРОЩЕННОГО АДМИНИСТРАТИВНОГО ENDPOINT")
    print("=" * 80)
    
    # Тест 1: Создание нового пользователя с минимальными параметрами
    print(f"\nТЕСТ 1: Создание нового пользователя с минимальными параметрами")
    print("-" * 60)
    
    test_email = f"testuser_{int(time.time())}@example.com"
    
    payload = {
        "user_email": test_email
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    try:
        print(f"Отправка запроса на создание пользователя: {test_email}")
        print(f"URL: {BASE_URL}/api/vpn/admin/simple-generate-config/")
        print(f"Headers: {headers}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            f"{BASE_URL}/api/vpn/admin/simple-generate-config/",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"\nОтвет сервера:")
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ Запрос выполнен успешно!")
            
            # Проверяем основные поля ответа
            if response_data.get('success'):
                print("✅ Статус success: True")
                
                # Проверяем информацию о пользователе
                user_info = response_data.get('user_info', {})
                if user_info:
                    print(f"✅ Пользователь создан: {user_info.get('email')}")
                    print(f"   - ID: {user_info.get('id')}")
                    print(f"   - Создан новый: {response_data.get('user_created', False)}")
                    print(f"   - Подписка создана: {user_info.get('subscription_created', False)}")
                
                # Проверяем Hiddify пользователя
                hiddify_uuid = response_data.get('hiddify_user_uuid')
                if hiddify_uuid:
                    print(f"✅ Hiddify пользователь: {hiddify_uuid}")
                    print(f"   - Создан новый: {response_data.get('hiddify_user_created', False)}")
                
                # Проверяем план и локацию
                plan_info = response_data.get('plan_info', {})
                location_info = response_data.get('location_info', {})
                
                if plan_info:
                    print(f"✅ План подписки: {plan_info.get('name')}")
                    print(f"   - Трафик: {plan_info.get('traffic_limit_gb')} GB")
                    print(f"   - Устройства: {plan_info.get('max_devices')}")
                
                if location_info:
                    print(f"✅ Локация: {location_info.get('name')}")
                    print(f"   - Страна: {location_info.get('country_code')}")
                    print(f"   - Сервер: {location_info.get('server_info', {}).get('server')}")
                
                # Проверяем конфигурацию
                config = response_data.get('config', {})
                if config:
                    print("✅ SingBox конфигурация сгенерирована")
                    
                    # Проверяем основные секции
                    sections = ['dns', 'inbounds', 'outbounds', 'route']
                    for section in sections:
                        if section in config:
                            print(f"   ✅ Секция '{section}' присутствует")
                        else:
                            print(f"   ❌ Секция '{section}' отсутствует")
                    
                    # Проверяем outbound'ы
                    outbounds = config.get('outbounds', [])
                    protocol_outbounds = [ob for ob in outbounds if ob.get('type') in ['trojan', 'vmess']]
                    print(f"   ✅ Протокольных outbound'ов: {len(protocol_outbounds)}")
                    
                    # Проверяем UUID в outbound'ах
                    uuid_found = False
                    for outbound in protocol_outbounds:
                        if outbound.get('type') == 'trojan' and outbound.get('password') == hiddify_uuid:
                            uuid_found = True
                            print(f"   ✅ Trojan UUID корректен: {outbound.get('tag')}")
                        elif outbound.get('type') == 'vmess' and outbound.get('uuid') == hiddify_uuid:
                            uuid_found = True
                            print(f"   ✅ VMess UUID корректен: {outbound.get('tag')}")
                    
                    if uuid_found:
                        print("   ✅ UUID пользователя корректно подставлен")
                    else:
                        print("   ❌ UUID пользователя не найден в outbound'ах")
                
                # Сохраняем результат
                result_filename = f"simple_admin_test_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(result_filename, 'w', encoding='utf-8') as f:
                    json.dump(response_data, f, indent=2, ensure_ascii=False, default=str)
                print(f"✅ Результат сохранен в файл: {result_filename}")
                
            else:
                print(f"❌ Статус success: False")
                print(f"Ошибка: {response_data.get('error', 'Unknown error')}")
                
        else:
            print(f"❌ HTTP ошибка: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Детали ошибки: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Текст ошибки: {response.text}")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Ошибка запроса: {e}")
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
    
    # Тест 2: Запрос без API ключа (должен вернуть 401/403)
    print(f"\n\nТЕСТ 2: Запрос без API ключа")
    print("-" * 60)
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/vpn/admin/simple-generate-config/",
            headers={"Content-Type": "application/json"},
            json={"user_email": "<EMAIL>"},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [401, 403]:
            print("✅ Корректно отклонен запрос без API ключа")
        else:
            print(f"❌ Неожиданный статус код: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Ошибка в тесте 2: {e}")
    
    # Тест 3: Запрос с неверным API ключом
    print(f"\n\nТЕСТ 3: Запрос с неверным API ключом")
    print("-" * 60)
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/vpn/admin/simple-generate-config/",
            headers={
                "Content-Type": "application/json",
                "X-API-Key": "invalid-api-key-12345"
            },
            json={"user_email": "<EMAIL>"},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [401, 403]:
            print("✅ Корректно отклонен запрос с неверным API ключом")
        else:
            print(f"❌ Неожиданный статус код: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Ошибка в тесте 3: {e}")
    
    # Тест 4: Запрос с API ключом в параметре
    print(f"\n\nТЕСТ 4: API ключ в параметре запроса")
    print("-" * 60)
    
    test_email_2 = f"testuser_param_{int(time.time())}@example.com"
    
    payload_with_key = {
        "user_email": test_email_2,
        "api_key": API_KEY,
        "plan_name": "Premium",
        "location_country": "NL"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/vpn/admin/simple-generate-config/",
            headers={"Content-Type": "application/json"},
            json=payload_with_key,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API ключ в параметре работает корректно")
            response_data = response.json()
            if response_data.get('success'):
                print(f"✅ Пользователь создан: {response_data.get('user_info', {}).get('email')}")
        else:
            print(f"❌ Неожиданный статус код: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Ошибка: {error_data.get('error', 'Unknown')}")
            except:
                print(f"Текст ошибки: {response.text}")
                
    except Exception as e:
        print(f"❌ Ошибка в тесте 4: {e}")
    
    print("\n" + "=" * 80)
    print("ТЕСТИРОВАНИЕ ЗАВЕРШЕНО")
    print("=" * 80)


def test_swagger_availability():
    """Проверяет доступность Swagger документации."""
    print(f"\nПроверка Swagger документации...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/docs/", timeout=10)
        if response.status_code == 200:
            print("✅ Swagger UI доступен")
        else:
            print(f"❌ Swagger UI недоступен: {response.status_code}")
    except Exception as e:
        print(f"❌ Ошибка доступа к Swagger: {e}")


if __name__ == "__main__":
    print("Запуск тестирования упрощенного административного endpoint...")
    test_swagger_availability()
    test_simple_endpoint()
    print("\nТестирование завершено!")
