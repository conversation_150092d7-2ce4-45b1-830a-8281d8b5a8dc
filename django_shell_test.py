#!/usr/bin/env python3
"""
Тестирование endpoint через Django shell
"""
import os
import sys
import django
import json
from datetime import datetime

# Настройка Django
sys.path.append('/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

# Добавляем testserver в ALLOWED_HOSTS для тестирования
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

from django.test import Client
from django.contrib.auth import get_user_model

def test_admin_endpoint():
    """Тестирует admin endpoint через Django test client"""
    
    client = Client()
    
    url = '/api/vpn/admin/simple-generate-config/'
    headers = {
        'HTTP_X_API_KEY': 'be84eb6e-cf9d-4b2b-b063-fdf26960ebca'
    }
    data = {
        'user_email': '<EMAIL>'
    }
    
    print("=== ТЕСТИРОВАНИЕ ЧЕРЕЗ DJANGO CLIENT ===")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(data, indent=2)}")
    print("-" * 50)
    
    try:
        response = client.post(url, data=data, content_type='application/json', **headers)
        
        print(f"HTTP Status: {response.status_code}")
        print(f"Response headers: {dict(response.items())}")
        print("-" * 50)
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ Запрос успешен!")
            
            # Сохраняем ответ в файл
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'/root/matrix/django_shell_test_result_{timestamp}.json'
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, indent=2, ensure_ascii=False)
            
            print(f"Ответ сохранен в {filename}")
            
            # Анализ структуры
            print("\n=== АНАЛИЗ СТРУКТУРЫ ОТВЕТА ===")
            print(f"Основные ключи: {list(response_data.keys())}")
            
            if 'config' in response_data:
                config = response_data['config']
                print("✅ Поле 'config' присутствует")
                print(f"Ключи конфигурации: {list(config.keys())}")
                
                # Проверяем основные разделы
                required_sections = ['dns', 'inbounds', 'outbounds', 'route']
                for section in required_sections:
                    if section in config:
                        print(f"✅ Раздел '{section}' присутствует")
                        if section == 'outbounds':
                            outbounds = config[section]
                            print(f"   - Количество outbounds: {len(outbounds)}")
                            
                            # Анализируем типы протоколов
                            protocols = []
                            for outbound in outbounds:
                                if 'type' in outbound:
                                    protocols.append(outbound['type'])
                            print(f"   - Протоколы: {', '.join(set(protocols))}")
                    else:
                        print(f"❌ Раздел '{section}' отсутствует")
            else:
                print("❌ Поле 'config' отсутствует")
            
            return True, response_data
            
        else:
            print(f"❌ Ошибка HTTP {response.status_code}")
            print(f"Response: {response.content.decode()}")
            return False, None
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def compare_with_example():
    """Сравнивает структуру ответа с эталонным примером"""
    
    print("\n=== СРАВНЕНИЕ С ЭТАЛОННЫМ ПРИМЕРОМ ===")
    
    # Загружаем эталонный пример
    try:
        with open('/root/matrix/singbox_Config_example', 'r', encoding='utf-8') as f:
            example_config = json.load(f)
        print("✅ Эталонный пример загружен")
    except Exception as e:
        print(f"❌ Ошибка загрузки эталонного примера: {e}")
        return False
    
    # Тестируем endpoint
    success, response_data = test_admin_endpoint()
    
    if not success or not response_data:
        print("❌ Не удалось получить ответ от endpoint")
        return False
    
    if 'config' not in response_data:
        print("❌ Поле 'config' отсутствует в ответе")
        return False
    
    actual_config = response_data['config']
    
    print("\n=== ДЕТАЛЬНОЕ СРАВНЕНИЕ СТРУКТУРЫ ===")
    
    def compare_structure(example, actual, path=""):
        """Рекурсивно сравнивает структуру двух объектов"""
        issues = []
        
        if isinstance(example, dict) and isinstance(actual, dict):
            # Проверяем ключи
            example_keys = set(example.keys())
            actual_keys = set(actual.keys())
            
            missing_keys = example_keys - actual_keys
            extra_keys = actual_keys - example_keys
            
            for key in missing_keys:
                issues.append(f"❌ Отсутствует ключ: {path}.{key}")
            
            for key in extra_keys:
                issues.append(f"ℹ️  Дополнительный ключ: {path}.{key}")
            
            # Рекурсивно проверяем общие ключи
            for key in example_keys & actual_keys:
                sub_issues = compare_structure(
                    example[key], 
                    actual[key], 
                    f"{path}.{key}" if path else key
                )
                issues.extend(sub_issues)
                
        elif isinstance(example, list) and isinstance(actual, list):
            if len(example) > 0 and len(actual) > 0:
                # Сравниваем структуру первого элемента
                sub_issues = compare_structure(
                    example[0], 
                    actual[0], 
                    f"{path}[0]"
                )
                issues.extend(sub_issues)
        
        elif type(example) != type(actual):
            issues.append(f"❌ Несоответствие типов в {path}: ожидается {type(example).__name__}, получен {type(actual).__name__}")
        
        return issues
    
    issues = compare_structure(example_config, actual_config)
    
    if not issues:
        print("✅ Структура полностью соответствует эталонному примеру!")
        return True
    else:
        print(f"Найдено {len(issues)} различий:")
        for issue in issues[:20]:  # Показываем первые 20 проблем
            print(f"  {issue}")
        if len(issues) > 20:
            print(f"  ... и еще {len(issues) - 20} различий")
        return False

if __name__ == "__main__":
    compare_with_example()
