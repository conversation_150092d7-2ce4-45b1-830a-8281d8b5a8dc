{"dns": {"servers": [{"tag": "cloudflare", "address": "https://*******/dns-query", "address_resolver": "local", "detour": "proxy"}, {"tag": "cloudflare-tls", "address": "tls://*******", "address_resolver": "local", "detour": "proxy"}, {"tag": "google", "address": "tls://*******", "address_resolver": "local", "detour": "proxy"}, {"tag": "local", "address": "*********", "detour": "direct"}, {"tag": "block", "address": "rcode://success"}], "rules": [{"outbound": "any", "server": "local"}, {"geoip": ["private"], "server": "local"}, {"geoip": ["cn"], "server": "local"}, {"domain_suffix": [".cn", ".ru"], "server": "local"}], "final": "cloudflare", "strategy": "ipv4_only", "disable_cache": false, "disable_expire": false}, "inbounds": [{"type": "tun", "inet4_address": "**********/30", "inet6_address": "fdfe:dcba:9876::1/126", "auto_route": true, "strict_route": false, "sniff": true, "sniff_override_destination": false, "domain_strategy": "ipv4_only"}], "outbounds": [{"type": "selector", "tag": "proxy", "outbounds": ["trojan-ws", "vmess-ws", "vmess-httpupgrade", "trojan-grpc", "vmess-grpc"]}, {"type": "trojan", "tag": "trojan-ws", "server": "***********", "server_port": 443, "password": "35593279-00d0-4ff8-911f-357441105e5c", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "http/1.1", "utls": {"enabled": true, "fingerprint": "chrome"}}, "transport": {"type": "ws", "path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx", "headers": {"Host": "***********.sslip.io"}, "early_data_header_name": "Sec-WebSocket-Protocol"}}, {"type": "vmess", "tag": "vmess-ws", "server": "***********", "server_port": 443, "uuid": "35593279-00d0-4ff8-911f-357441105e5c", "security": "auto", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "http/1.1", "utls": {"enabled": true, "fingerprint": "chrome"}}, "packet_encoding": "xudp", "transport": {"type": "ws", "path": "/39m0pgSOrY19tjCyr3egnx", "headers": {"Host": "***********.sslip.io"}, "early_data_header_name": "Sec-WebSocket-Protocol"}}, {"type": "vmess", "tag": "vmess-httpupgrade", "server": "***********", "server_port": 443, "uuid": "35593279-00d0-4ff8-911f-357441105e5c", "security": "auto", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "http/1.1", "utls": {"enabled": true, "fingerprint": "chrome"}}, "packet_encoding": "xudp", "transport": {"type": "httpupgrade", "path": "/39m0pgSOdKbicJLIaR", "headers": {"Host": "***********.sslip.io"}}}, {"type": "trojan", "tag": "trojan-grpc", "server": "***********", "server_port": 443, "password": "35593279-00d0-4ff8-911f-357441105e5c", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "h2", "utls": {"enabled": true, "fingerprint": "chrome"}}, "transport": {"type": "grpc", "service_name": "Cgm6B1DqLOKIFOh7gdS9", "idle_timeout": "1m55s", "ping_timeout": "15s"}}, {"type": "vmess", "tag": "vmess-grpc", "server": "***********", "server_port": 443, "uuid": "35593279-00d0-4ff8-911f-357441105e5c", "security": "auto", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "h2", "utls": {"enabled": true, "fingerprint": "chrome"}}, "packet_encoding": "xudp", "transport": {"type": "grpc", "service_name": "39m0pgSOOh7gdS9", "idle_timeout": "1m55s", "ping_timeout": "15s"}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns-out"}], "route": {"rules": [{"protocol": "dns", "outbound": "dns-out"}, {"geoip": ["private"], "outbound": "direct"}, {"geoip": ["cn"], "outbound": "direct"}, {"domain_suffix": [".cn", ".ru"], "outbound": "direct"}], "final": "proxy", "auto_detect_interface": true}}