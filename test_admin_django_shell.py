#!/usr/bin/env python3
"""
Тест административного endpoint через Django shell.
"""

import os
import sys
import django
import json
from datetime import datetime, timedelta

# Настройка Django
sys.path.insert(0, '/root/matrix')
sys.path.insert(0, '/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import force_authenticate
from accounts.models import UserAccount, HiddifyLink
from subscriptions.models import SubscriptionPlan, ActiveSubscription
from vpn.models import Location, SubscriptionPlanLocation
from vpn.admin_views import generate_personalized_config

User = get_user_model()


def create_test_data():
    """Создает минимальные тестовые данные."""
    print("Создание тестовых данных...")
    
    # Администратор
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'is_staff': True,
            'is_superuser': True,
            'is_active': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
    
    # Тестовый пользователь
    test_user, created = UserAccount.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'is_anonymous': False,
            'is_active': True
        }
    )
    
    # Локация
    test_location, created = Location.objects.get_or_create(
        name='Test Netherlands',
        defaults={
            'country_code': 'NL',
            'city': 'Amsterdam',
            'flag_emoji': '🇳🇱',
            'is_active': True,
            'hiddify_params': {
                'server': '***********',
                'server_port': 443,
                'tls_server_name': '***********.sslip.io',
                'trojan_ws_path': '/Cgm6B1DqLOKIFrY19tjCyr3egnx',
                'vmess_ws_path': '/39m0pgSOrY19tjCyr3egnx',
                'vmess_httpupgrade_path': '/39m0pgSOdKbicJLIaR',
                'trojan_grpc_service': 'Cgm6B1DqLOKIFOh7gdS9',
                'vmess_grpc_service': '39m0pgSOOh7gdS9'
            }
        }
    )
    
    # План подписки
    test_plan, created = SubscriptionPlan.objects.get_or_create(
        name='Test Premium Plan',
        defaults={
            'price': 9.99,
            'currency': 'EUR',
            'duration_days': 30,
            'traffic_limit_gb': 100,
            'max_devices': 5,
            'is_active': True
        }
    )
    # Создаем связь план-локация через промежуточную модель
    plan_location, created = SubscriptionPlanLocation.objects.get_or_create(
        plan=test_plan,
        location=test_location,
        defaults={'is_default': True}
    )
    
    # Активная подписка
    active_subscription, created = ActiveSubscription.objects.get_or_create(
        user=test_user,
        plan=test_plan,
        defaults={
            'start_date': timezone.now(),
            'end_date': timezone.now() + timedelta(days=30),
            'is_active': True
        }
    )
    
    # Hiddify связь
    hiddify_link, created = HiddifyLink.objects.get_or_create(
        user=test_user,
        defaults={
            'hiddify_user_uuid': test_user.id,
            'is_active_in_hiddify': True
        }
    )
    
    print(f"✓ Администратор: {admin_user.email}")
    print(f"✓ Тестовый пользователь: {test_user.email}")
    print(f"✓ Локация: {test_location.name}")
    print(f"✓ План: {test_plan.name}")
    print(f"✓ Активная подписка: {active_subscription.is_active}")
    
    return admin_user, test_user, test_location, test_plan


def test_admin_view_directly():
    """Тестирует административный view напрямую."""
    print("\n" + "=" * 80)
    print("ТЕСТИРОВАНИЕ АДМИНИСТРАТИВНОГО VIEW")
    print("=" * 80)
    
    # Создаем тестовые данные
    admin_user, test_user, test_location, test_plan = create_test_data()
    
    # Создаем фабрику запросов
    factory = RequestFactory()
    
    # Тест 1: Успешный запрос
    print(f"\nТЕСТ 1: Успешная генерация конфигурации")
    print(f"User ID: {test_user.id}")
    
    request_data = {
        'user_id': str(test_user.id),
        'force_recreate': False
    }
    
    try:
        # Создаем POST запрос
        request = factory.post(
            '/api/vpn/admin/generate-personalized-config/',
            data=json.dumps(request_data),
            content_type='application/json'
        )
        
        # Аутентифицируем как администратор
        force_authenticate(request, user=admin_user)
        
        # Вызываем view
        response = generate_personalized_config(request)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.data
            print("✓ Запрос выполнен успешно")
            
            # Проверяем структуру ответа
            if response_data.get('success'):
                print("✓ Статус success: True")
                
                # Проверяем основные поля
                required_fields = ['user_info', 'config', 'metadata']
                for field in required_fields:
                    if field in response_data:
                        print(f"✓ Поле '{field}' присутствует")
                    else:
                        print(f"✗ Поле '{field}' отсутствует")
                
                # Проверяем конфигурацию
                config = response_data.get('config', {})
                if config:
                    print("✓ Конфигурация сгенерирована")
                    
                    # Проверяем структуру
                    config_sections = ['dns', 'inbounds', 'outbounds', 'route']
                    for section in config_sections:
                        if section in config:
                            print(f"  ✓ Секция '{section}' присутствует")
                        else:
                            print(f"  ✗ Секция '{section}' отсутствует")
                    
                    # Проверяем outbound'ы
                    outbounds = config.get('outbounds', [])
                    if outbounds:
                        protocol_outbounds = [ob for ob in outbounds if ob.get('type') in ['trojan', 'vmess']]
                        print(f"  ✓ Найдено {len(protocol_outbounds)} протокольных outbound'ов")
                        
                        # Проверяем UUID подстановку
                        uuid_correct_count = 0
                        for outbound in protocol_outbounds:
                            if outbound.get('type') == 'trojan':
                                if outbound.get('password') == str(test_user.id):
                                    uuid_correct_count += 1
                                    print(f"    ✓ Trojan '{outbound.get('tag')}': UUID корректен")
                                else:
                                    print(f"    ✗ Trojan '{outbound.get('tag')}': UUID некорректен")
                            elif outbound.get('type') == 'vmess':
                                if outbound.get('uuid') == str(test_user.id):
                                    uuid_correct_count += 1
                                    print(f"    ✓ VMess '{outbound.get('tag')}': UUID корректен")
                                else:
                                    print(f"    ✗ VMess '{outbound.get('tag')}': UUID некорректен")
                        
                        if uuid_correct_count == len(protocol_outbounds):
                            print("  ✓ Все UUID корректно подставлены")
                        else:
                            print(f"  ✗ Корректных UUID: {uuid_correct_count}/{len(protocol_outbounds)}")
                    else:
                        print("  ✗ Outbound'ы не найдены")
                else:
                    print("✗ Конфигурация не сгенерирована")
                
                # Сохраняем результат
                result_filename = f"django_shell_test_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(result_filename, 'w', encoding='utf-8') as f:
                    json.dump(response_data, f, indent=2, ensure_ascii=False, default=str)
                print(f"✓ Результат сохранен в файл: {result_filename}")
                
            else:
                print(f"✗ Статус success: False")
                print(f"Ошибка: {response_data.get('error', 'Unknown error')}")
        else:
            print(f"✗ Ошибка HTTP: {response.status_code}")
            if hasattr(response, 'data'):
                print(f"Детали: {response.data}")
                
    except Exception as e:
        print(f"✗ Ошибка выполнения: {e}")
        import traceback
        traceback.print_exc()
    
    # Тест 2: Ошибка с несуществующим пользователем
    print(f"\nТЕСТ 2: Ошибка с несуществующим пользователем")
    
    fake_request_data = {
        'user_id': '00000000-0000-0000-0000-000000000000',
        'force_recreate': False
    }
    
    try:
        request = factory.post(
            '/api/vpn/admin/generate-personalized-config/',
            data=json.dumps(fake_request_data),
            content_type='application/json'
        )
        force_authenticate(request, user=admin_user)
        
        response = generate_personalized_config(request)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✓ Корректно возвращена ошибка 400")
            if hasattr(response, 'data') and not response.data.get('success', True):
                print("✓ Статус success: False")
                print(f"✓ Сообщение об ошибке: {response.data.get('error', 'No error message')}")
        else:
            print(f"✗ Неожиданный статус код: {response.status_code}")
            
    except Exception as e:
        print(f"✗ Ошибка в тесте 2: {e}")
    
    print("\n" + "=" * 80)
    print("ТЕСТИРОВАНИЕ ЗАВЕРШЕНО")
    print("=" * 80)


if __name__ == "__main__":
    print("Запуск тестирования через Django shell...")
    test_admin_view_directly()
    print("\nТестирование завершено!")
