# Диагностический отчет: Проблемы с протоколом Trojan в Hiddify Manager

## Обнаруженные проблемы

### 1. Нестандартная архитектура Trojan
**Проблема:** Hiddify Manager использует нестандартную реализацию Trojan протокола:
- Trojan работает через HTTP пути вместо нативного TLS
- HAProxy выполняет TLS терминацию и маршрутизирует по HTTP заголовкам
- Xray получает уже расшифрованный трафик через Unix sockets

**Найденные конфигурации:**
```
# HAProxy маршрутизация
use_backend trojant if { path_beg /Cgm6B1DqLOKIFEh03ACaGoW }
use_backend trojanw if { path_beg /Cgm6B1DqLOKIFrY19tjCyr3egnx }

# Xray конфигурация
"listen": "@@trojan-tcp-new"  # Unix socket
"security": "none"            # Без TLS!
"header": {
  "type": "http",
  "request": {
    "path": ["/Cgm6B1DqLOKIFEh03ACaGoW"]
  }
}
```

### 2. Отсутствие нативного TLS Trojan
**Проблема:** Нет конфигурации для стандартного Trojan с прямым TLS подключением
- Все Trojan inbound'ы используют `"security": "none"`
- TLS обрабатывается только на уровне HAProxy
- Стандартные Trojan клиенты не могут подключиться

### 3. Неправильная конфигурация клиентов
**Проблема:** Генерируемые конфигурации не соответствуют реальной архитектуре
- Клиентские конфигурации указывают на прямой TLS Trojan
- Фактически сервер ожидает HTTP запросы с определенными путями

## Анализ соответствия документации

### Официальная документация Hiddify
Согласно документации, Hiddify поддерживает:
- Trojan TLS TCP
- Trojan TLS WS  
- Trojan TLS gRPC
- Trojan TLS H2

### Реальная реализация
Фактически реализовано:
- Trojan через HTTP пути (нестандартно)
- Trojan через WebSocket (работает)
- TLS терминация на HAProxy, а не в Xray

## Тестирование функциональности

### Результаты тестов
1. **Стандартный Trojan TCP:** ❌ Не работает
2. **Trojan с HTTP transport:** ⚠️ Требует специальной конфигурации
3. **Trojan WebSocket:** ✅ Должен работать
4. **SSL сертификаты:** ✅ Корректные для ductuspro.ru

### Логи и диагностика
- Xray сервис: ✅ Работает
- HAProxy: ✅ Работает  
- Unix sockets: ✅ Созданы
- Порты 80/443: ✅ Открыты

## Рекомендации по исправлению

### 1. Для совместимости с SingBox (Stage 1)
Использовать Trojan WebSocket вместо TCP:
```json
{
  "type": "trojan",
  "server": "ductuspro.ru", 
  "server_port": 443,
  "password": "user-uuid",
  "transport": {
    "type": "ws",
    "path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx"
  },
  "tls": {
    "enabled": true,
    "server_name": "ductuspro.ru"
  }
}
```

### 2. Альтернативное решение: Reality
Использовать Reality протокол для лучшей совместимости:
- Reality работает с нативным TLS
- Лучше обходит блокировки
- Полная совместимость с SingBox

### 3. Настройка прямого TLS Trojan (если нужно)
Требует изменения конфигурации Hiddify:
- Добавить отдельный порт для нативного Trojan
- Настроить Xray с `"security": "tls"`
- Обновить HAProxy для TCP passthrough

## Выводы

Основная причина неработоспособности Trojan конфигураций:
1. **Архитектурное несоответствие** между ожиданиями клиентов и реализацией сервера
2. **Нестандартная реализация** Trojan протокола в Hiddify Manager
3. **Отсутствие нативного TLS** в Xray конфигурациях

Для Stage 1 проекта рекомендуется:
- Использовать **Trojan WebSocket** для совместимости
- Рассмотреть **Reality** как основной протокол
- Избегать **Trojan TCP** до исправления архитектуры
