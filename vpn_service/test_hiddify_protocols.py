#!/usr/bin/env python3
"""
Диагностический скрипт для анализа проблемы с протоколами в Hiddify Manager.
Проверяет, какие протоколы возвращает Hiddify Manager API.
"""
import os
import sys
import django
import requests
import json
from typing import Dict, Any

# Настройка Django
sys.path.append('/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

from vpn.services import HiddifyApiService
from django.conf import settings

def test_hiddify_api():
    """Тестирует API Hiddify Manager на предмет поддержки протоколов."""
    print("=" * 60)
    print("ДИАГНОСТИКА ПРОТОКОЛОВ HIDDIFY MANAGER")
    print("=" * 60)
    
    hiddify_service = HiddifyApiService()
    
    # Тестовый UUID пользователя
    test_uuid = "15c175d8-703c-456a-ac82-91041f8af845"
    
    print(f"1. Тестирование пользователя: {test_uuid}")
    print(f"2. Admin URL: {settings.HIDDIFY_ADMIN_BASE_URL}")
    print(f"3. User URL: {settings.HIDDIFY_USER_BASE_URL}")
    print("-" * 60)
    
    # Тест 1: Получение SingBox конфигурации
    print("ТЕСТ 1: Получение SingBox конфигурации")
    success, response = hiddify_service.get_singbox_config_for_user(test_uuid)
    
    print(f"Успех: {success}")
    print(f"Тип ответа: {type(response)}")
    
    if success and isinstance(response, dict):
        if 'config' in response:
            config = response['config']
            print(f"Тип конфигурации: {type(config)}")
            
            if isinstance(config, dict):
                analyze_config_dict(config)
            elif isinstance(config, str):
                analyze_config_string(config)
        else:
            print("Ключ 'config' не найден в ответе")
            print(f"Доступные ключи: {list(response.keys())}")
    else:
        print(f"Ошибка или неверный формат ответа: {response}")
    
    print("-" * 60)
    
    # Тест 2: Прямой запрос к пользовательскому endpoint
    print("ТЕСТ 2: Прямой запрос к пользовательскому endpoint")
    test_direct_user_endpoint(test_uuid)
    
    print("-" * 60)
    
    # Тест 3: Проверка админского API
    print("ТЕСТ 3: Проверка админского API")
    test_admin_api()

def analyze_config_dict(config: Dict[str, Any]):
    """Анализирует конфигурацию в формате словаря."""
    print("Анализ конфигурации (словарь):")
    
    if 'outbounds' in config:
        outbounds = config['outbounds']
        print(f"Найдено {len(outbounds)} outbound(s):")
        
        protocols_found = set()
        for i, outbound in enumerate(outbounds):
            if 'type' in outbound:
                protocol = outbound['type']
                protocols_found.add(protocol)
                print(f"  Outbound {i}: {protocol}")
                
                # Детальный анализ для VPN протоколов
                if protocol in ['vmess', 'vless', 'trojan', 'shadowsocks']:
                    print(f"    Сервер: {outbound.get('server', 'N/A')}")
                    print(f"    Порт: {outbound.get('server_port', 'N/A')}")
                    if 'transport' in outbound:
                        transport = outbound['transport']
                        print(f"    Транспорт: {transport.get('type', 'N/A')}")
        
        print(f"Найденные протоколы: {', '.join(protocols_found)}")
        
        # Проверка наличия Trojan
        if 'trojan' in protocols_found:
            print("✅ Протокол Trojan НАЙДЕН в конфигурации")
        else:
            print("❌ Протокол Trojan НЕ НАЙДЕН в конфигурации")
            
        if 'vmess' in protocols_found:
            print("✅ Протокол VMess НАЙДЕН в конфигурации")
        else:
            print("❌ Протокол VMess НЕ НАЙДЕН в конфигурации")
    else:
        print("Ключ 'outbounds' не найден в конфигурации")
        print(f"Доступные ключи: {list(config.keys())}")

def analyze_config_string(config: str):
    """Анализирует конфигурацию в формате строки."""
    print("Анализ конфигурации (строка):")
    print(f"Длина строки: {len(config)} символов")
    
    # Проверка на HTML
    if '<html' in config.lower() or '<!doctype' in config.lower():
        print("❌ Получена HTML страница вместо JSON конфигурации")
        print("Первые 200 символов:")
        print(config[:200])
        return
    
    # Попытка парсинга JSON
    try:
        parsed_config = json.loads(config)
        print("✅ Строка успешно распарсена как JSON")
        analyze_config_dict(parsed_config)
    except json.JSONDecodeError as e:
        print(f"❌ Ошибка парсинга JSON: {e}")
        print("Первые 500 символов:")
        print(config[:500])
    
    # Поиск протоколов в тексте
    protocols_in_text = []
    for protocol in ['trojan', 'vmess', 'vless', 'shadowsocks']:
        if protocol in config.lower():
            protocols_in_text.append(protocol)
    
    if protocols_in_text:
        print(f"Протоколы найденные в тексте: {', '.join(protocols_in_text)}")
    else:
        print("Протоколы не найдены в тексте конфигурации")

def test_direct_user_endpoint(user_uuid: str):
    """Тестирует прямой запрос к пользовательскому endpoint."""
    url = f"{settings.HIDDIFY_USER_BASE_URL}/{user_uuid}/singbox/"
    
    print(f"Запрос к: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"HTTP статус: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        
        if response.status_code == 200:
            content = response.text
            print(f"Размер ответа: {len(content)} символов")
            
            # Проверка типа контента
            if 'application/json' in response.headers.get('Content-Type', ''):
                try:
                    json_data = response.json()
                    print("✅ Получен валидный JSON")
                    analyze_config_dict(json_data)
                except json.JSONDecodeError:
                    print("❌ Ошибка парсинга JSON несмотря на Content-Type")
            else:
                print("Контент не является JSON, анализируем как текст...")
                analyze_config_string(content)
        else:
            print(f"❌ HTTP ошибка: {response.status_code}")
            print(f"Ответ: {response.text[:200]}")
            
    except requests.RequestException as e:
        print(f"❌ Ошибка запроса: {e}")

def test_admin_api():
    """Тестирует админский API."""
    url = f"{settings.HIDDIFY_ADMIN_BASE_URL}/api/v2/admin/user/"
    headers = {
        'Content-Type': 'application/json',
        'Hiddify-API-Key': settings.HIDDIFY_ADMIN_API_KEY,
    }
    
    print(f"Запрос к админскому API: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"HTTP статус: {response.status_code}")
        
        if response.status_code == 200:
            try:
                users_data = response.json()
                print(f"✅ Получен список пользователей: {len(users_data)} записей")
                
                # Анализ первого пользователя
                if users_data and len(users_data) > 0:
                    first_user = users_data[0]
                    print("Анализ первого пользователя:")
                    print(f"  UUID: {first_user.get('uuid', 'N/A')}")
                    print(f"  Имя: {first_user.get('name', 'N/A')}")
                    print(f"  Активен: {first_user.get('enable', 'N/A')}")
                    
                    # Поиск настроек протоколов
                    for key, value in first_user.items():
                        if 'protocol' in key.lower() or 'trojan' in key.lower() or 'vmess' in key.lower():
                            print(f"  {key}: {value}")
                            
            except json.JSONDecodeError:
                print("❌ Ошибка парсинга JSON ответа админского API")
        else:
            print(f"❌ HTTP ошибка админского API: {response.status_code}")
            print(f"Ответ: {response.text[:200]}")
            
    except requests.RequestException as e:
        print(f"❌ Ошибка запроса к админскому API: {e}")

if __name__ == "__main__":
    test_hiddify_api()
