#!/usr/bin/env python3
import requests
import json

def analyze_hiddify_config():
    """Детальный анализ конфигурации Hiddify Manager."""
    print("=" * 80)
    print("ДЕТАЛЬНЫЙ АНАЛИЗ КОНФИГУРАЦИИ HIDDIFY MANAGER")
    print("=" * 80)
    
    test_uuid = "15c175d8-703c-456a-ac82-91041f8af845"
    user_url = f"https://ductuspro.ru/UM9hbUMIptlMde2JRIjc0WeJAY/{test_uuid}/singbox/"
    
    print(f"Тестируемый пользователь: {test_uuid}")
    print(f"URL: {user_url}")
    print("-" * 80)
    
    try:
        response = requests.get(user_url, timeout=15)
        
        if response.status_code == 200:
            config = response.json()
            
            print("АНАЛИЗ КОНФИГУРАЦИИ:")
            print(f"Общее количество outbound: {len(config.get('outbounds', []))}")
            print()
            
            # Детальный анализ каждого outbound
            outbounds = config.get('outbounds', [])
            vpn_protocols = []
            
            for i, outbound in enumerate(outbounds):
                outbound_type = outbound.get('type', 'unknown')
                tag = outbound.get('tag', f'outbound-{i}')
                
                print(f"Outbound {i}: {outbound_type} (tag: {tag})")
                
                if outbound_type in ['vmess', 'vless', 'trojan', 'shadowsocks', 'hysteria', 'hysteria2']:
                    vpn_protocols.append(outbound_type)
                    
                    # Детальная информация о VPN протоколе
                    server = outbound.get('server', 'N/A')
                    port = outbound.get('server_port', 'N/A')
                    print(f"  ├─ Сервер: {server}")
                    print(f"  ├─ Порт: {port}")
                    
                    # Транспорт
                    if 'transport' in outbound:
                        transport = outbound['transport']
                        transport_type = transport.get('type', 'N/A')
                        print(f"  ├─ Транспорт: {transport_type}")
                        
                        if 'path' in transport:
                            print(f"  ├─ Путь: {transport['path']}")
                    
                    # TLS настройки
                    if 'tls' in outbound:
                        tls = outbound['tls']
                        tls_enabled = tls.get('enabled', False)
                        server_name = tls.get('server_name', 'N/A')
                        print(f"  ├─ TLS: {tls_enabled} (SNI: {server_name})")
                    
                    # UUID/Password
                    if 'uuid' in outbound:
                        print(f"  └─ UUID: {outbound['uuid']}")
                    elif 'password' in outbound:
                        print(f"  └─ Password: {outbound['password'][:8]}...")
                    
                elif outbound_type == 'selector':
                    # Анализ селектора
                    outbounds_list = outbound.get('outbounds', [])
                    print(f"  └─ Включает: {', '.join(outbounds_list)}")
                    
                elif outbound_type == 'urltest':
                    # Анализ URL теста
                    outbounds_list = outbound.get('outbounds', [])
                    print(f"  └─ Тестирует: {', '.join(outbounds_list)}")
                
                print()
            
            print("-" * 80)
            print("РЕЗЮМЕ АНАЛИЗА:")
            print(f"Найденные VPN протоколы: {', '.join(set(vpn_protocols)) if vpn_protocols else 'Нет'}")
            
            # Проверка наличия конкретных протоколов
            protocols_status = {
                'VMess': 'vmess' in vpn_protocols,
                'VLESS': 'vless' in vpn_protocols,
                'Trojan': 'trojan' in vpn_protocols,
                'Shadowsocks': 'shadowsocks' in vpn_protocols
            }
            
            for protocol, found in protocols_status.items():
                status = "✅ НАЙДЕН" if found else "❌ НЕ НАЙДЕН"
                print(f"{protocol}: {status}")
            
            print()
            print("ДИАГНОЗ ПРОБЛЕМЫ:")
            if not protocols_status['Trojan']:
                print("❌ ПРОБЛЕМА ПОДТВЕРЖДЕНА: Протокол Trojan отсутствует в конфигурации")
                print("   Hiddify Manager генерирует только VMess протокол для пользователей")
                print("   несмотря на то, что Trojan может быть включен в настройках панели")
            else:
                print("✅ Проблема не обнаружена: Trojan протокол присутствует")
            
            print()
            print("РЕКОМЕНДАЦИИ:")
            if not protocols_status['Trojan']:
                print("1. Проверить настройки протоколов в админ-панели Hiddify Manager")
                print("2. Убедиться, что Trojan протокол активирован для пользователей")
                print("3. Использовать гибридное решение с отдельным Trojan endpoint")
                print("4. Реализовать мульти-протокольный endpoint для объединения конфигураций")
            
            # Сохранение полной конфигурации для анализа
            with open('/root/matrix/vpn_service/hiddify_full_config.json', 'w') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"\n📁 Полная конфигурация сохранена в: hiddify_full_config.json")
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(response.text[:500])
            
    except Exception as e:
        print(f"❌ Ошибка анализа: {e}")

if __name__ == "__main__":
    analyze_hiddify_config()
