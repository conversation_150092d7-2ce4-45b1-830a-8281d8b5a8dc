{"outbounds": [{"type": "selector", "tag": "Select", "outbounds": ["Auto", "***********.sslip.io tls httpupgrade direct vmess § 443 1", "ductuspro tls httpupgrade direct vmess § 443 3"], "default": "Auto"}, {"type": "urltest", "tag": "Auto", "outbounds": ["***********.sslip.io tls httpupgrade direct vmess § 443 1", "ductuspro tls httpupgrade direct vmess § 443 3"], "url": "https://www.gstatic.com/generate_204", "interval": "10m", "tolerance": 200}, {"tag": "direct", "type": "direct"}, {"tag": "bypass", "type": "direct"}, {"tag": "block", "type": "block"}, {"tag": "dns-out", "type": "dns"}, {"tag": "***********.sslip.io tls httpupgrade direct vmess § 443 1", "type": "vmess", "server": "***********.sslip.io", "server_port": 443, "uuid": "15c175d8-703c-456a-ac82-91041f8af845", "tls": {"enabled": true, "server_name": "***********.sslip.io", "utls": {"enabled": true, "fingerprint": "chrome"}, "insecure": false, "alpn": ["http/1.1"]}, "alter_id": 0, "security": "auto", "packet_encoding": "xudp", "transport": {"type": "httpupgrade", "path": "/39m0pgSOdKbicJLIaR", "headers": {"Host": "***********.sslip.io"}}}, {"tag": "ductuspro tls httpupgrade direct vmess § 443 3", "type": "vmess", "server": "ductuspro.ru", "server_port": 443, "uuid": "15c175d8-703c-456a-ac82-91041f8af845", "tls": {"enabled": true, "server_name": "ductuspro.ru", "utls": {"enabled": true, "fingerprint": "chrome"}, "insecure": false, "alpn": ["http/1.1"]}, "alter_id": 0, "security": "auto", "packet_encoding": "xudp", "transport": {"type": "httpupgrade", "path": "/39m0pgSOdKbicJLIaR", "headers": {"Host": "ductuspro.ru"}}}], "route": {"auto_detect_interface": true, "override_android_vpn": true, "final": "Select", "rule_set": [{"tag": "geosite-ru", "type": "remote", "format": "binary", "url": "https://github.com/SagerNet/sing-geosite/raw/rule-set/geosite-category-ru.srs", "download_detour": "bypass"}, {"tag": "geoip-ru", "type": "remote", "format": "binary", "url": "https://github.com/SagerNet/sing-geoip/raw/rule-set/geoip-ru.srs", "download_detour": "bypass"}], "rules": [{"outbound": "dns-out", "port": [53]}, {"inbound": ["dns-in"], "outbound": "dns-out"}, {"domain_suffix": ["ru"], "outbound": "bypass"}, {"rule_set": "geoip-ru", "outbound": "bypass"}, {"rule_set": "geosite-ru", "outbound": "bypass"}, {"protocol": "quic", "port": [443], "outbound": "block"}, {"ip_cidr": ["*********/3", "ff00::/8"], "outbound": "block", "source_ip_cidr": ["*********/3", "ff00::/8"]}]}, "experimental": {"clash_api": {"external_controller": "127.0.0.1:9090", "external_ui_download_url": "https://github.com/MetaCubeX/Yacd-meta/archive/gh-pages.zip"}, "cache_file": {"enabled": true, "path": "cache.db", "cache_id": "15c175d8-703c-456a-ac82-91041f8af845", "store_fakeip": true}}, "dns": {"servers": [{"address": "tcp://*******", "address_resolver": "dns-local", "strategy": "prefer_ipv4", "tag": "dns-remote", "detour": "Select"}, {"address": "*******", "detour": "direct", "tag": "dns-local"}, {"address": "rcode://success", "tag": "dns-block"}], "rules": [{"domain": ["github.com", "githubusercontent.com", "raw.githubusercontent.com", "*******", "***********.sslip.io", "www.genderit.org", "ductuspro.ru"], "server": "dns-local"}, {"domain_suffix": ["ru"], "server": "dns-local"}, {"outbound": "direct", "server": "dns-local"}], "final": "dns-remote", "reverse_mapping": true, "strategy": "prefer_ipv4", "independent_cache": true}, "inbounds": [{"listen": "127.0.0.1", "listen_port": 6450, "override_address": "*******", "override_port": 53, "tag": "dns-in", "type": "direct"}, {"type": "tun", "tag": "tun-in", "domain_strategy": "prefer_ipv4", "interface_name": "tun0", "address": ["**********/30"], "mtu": 9000, "auto_route": true, "strict_route": true, "stack": "system", "endpoint_independent_nat": true, "sniff": true, "sniff_override_destination": false}, {"domain_strategy": "prefer_ipv4", "listen": "127.0.0.1", "listen_port": 2334, "sniff": true, "sniff_override_destination": false, "tag": "mixed-in", "type": "mixed"}]}