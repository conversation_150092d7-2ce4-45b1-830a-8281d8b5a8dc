#!/usr/bin/env python3
import requests
import json

# Тестирование прямого запроса к Hiddify Manager
def test_hiddify_direct():
    print("Тестирование прямого запроса к Hiddify Manager...")
    
    # Тестовый UUID
    test_uuid = "15c175d8-703c-456a-ac82-91041f8af845"
    user_url = f"https://ductuspro.ru/UM9hbUMIptlMde2JRIjc0WeJAY/{test_uuid}/singbox/"
    
    print(f"URL: {user_url}")
    
    try:
        response = requests.get(user_url, timeout=15)
        print(f"HTTP Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        print(f"Content Length: {len(response.text)}")
        
        if response.status_code == 200:
            content = response.text
            
            # Проверка на JSON
            try:
                json_data = json.loads(content)
                print("✅ Получен валидный JSON")
                
                # Анализ outbounds
                if 'outbounds' in json_data:
                    outbounds = json_data['outbounds']
                    print(f"Найдено {len(outbounds)} outbound(s):")
                    
                    for i, outbound in enumerate(outbounds):
                        if 'type' in outbound:
                            protocol = outbound['type']
                            print(f"  {i}: {protocol}")
                            
                            if protocol in ['vmess', 'vless', 'trojan']:
                                print(f"    Server: {outbound.get('server', 'N/A')}")
                                print(f"    Port: {outbound.get('server_port', 'N/A')}")
                else:
                    print("Ключ 'outbounds' не найден")
                    print(f"Доступные ключи: {list(json_data.keys())}")
                    
            except json.JSONDecodeError:
                print("❌ Не удалось распарсить как JSON")
                print("Первые 500 символов:")
                print(content[:500])
                
                # Поиск протоколов в тексте
                if 'trojan' in content.lower():
                    print("✅ Найден 'trojan' в тексте")
                if 'vmess' in content.lower():
                    print("✅ Найден 'vmess' в тексте")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(response.text[:200])
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")

if __name__ == "__main__":
    test_hiddify_direct()
