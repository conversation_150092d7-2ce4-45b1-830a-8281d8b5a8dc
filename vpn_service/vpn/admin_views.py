"""
Административные API views для VPN приложения.
"""
import logging
import json
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiExample

from .permissions import IsAdminUser, HasValidAPIKey, IsAdminUserOrHasAPIKey
from .admin_serializers import (
    AdminGenerateConfigRequestSerializer,
    AdminGenerateConfigResponseSerializer,
    AdminGenerateConfigErrorSerializer,
    UserInfoSerializer,
    LocationInfoSerializer,
    SimpleAdminConfigRequestSerializer,
    SimpleAdminConfigResponseSerializer
)
from .trojan_service import PersonalizedSingBoxService
from .models import Location
from accounts.models import UserAccount, HiddifyLink
from subscriptions.models import ActiveSubscription

logger = logging.getLogger(__name__)


@extend_schema(
    tags=['Admin'],
    summary='Generate personalized SingBox config',
    description="""
    Административный endpoint для генерации персонализированных SingBox конфигураций.
    
    **Функциональность:**
    - Генерирует уникальную SingBox конфигурацию для указанного пользователя
    - Использует данные пользователя из Hiddify Manager
    - Применяет параметры локации из активной подписки
    - Возвращает конфигурацию в формате, идентичном singbox_Config_example
    
    **Права доступа:**
    - Только администраторы (staff пользователи)
    - Требуется JWT аутентификация
    
    **Параметры:**
    - `user_id`: UUID пользователя в системе
    - `force_recreate`: Принудительно пересоздать пользователя в Hiddify (опционально)
    
    **Логика работы:**
    1. Проверяет существование пользователя
    2. Определяет активную подписку и доступные локации
    3. Получает или создает пользователя в Hiddify Manager
    4. Генерирует персонализированную SingBox конфигурацию
    5. Возвращает конфигурацию с метаданными
    """,
    request=AdminGenerateConfigRequestSerializer,
    responses={
        200: AdminGenerateConfigResponseSerializer,
        400: AdminGenerateConfigErrorSerializer,
        401: AdminGenerateConfigErrorSerializer,
        403: AdminGenerateConfigErrorSerializer,
        404: AdminGenerateConfigErrorSerializer,
        500: AdminGenerateConfigErrorSerializer
    },
    examples=[
        OpenApiExample(
            'Request Example',
            description='Пример запроса генерации конфигурации',
            value={
                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                "force_recreate": False
            },
            request_only=True
        ),
        OpenApiExample(
            'Success Response',
            description='Успешная генерация конфигурации',
            value={
                "success": True,
                "user_info": {
                    "id": "123e4567-e89b-12d3-a456-426614174000",
                    "email": "<EMAIL>",
                    "is_anonymous": False,
                    "is_active": True,
                    "active_subscription": {
                        "plan_name": "Premium Monthly",
                        "expires_at": "2025-07-14T12:00:00Z",
                        "traffic_limit_gb": 100,
                        "max_devices": 5
                    },
                    "device_count": 2
                },
                "hiddify_user_uuid": "15c175d8-703c-456a-ac82-91041f8af845",
                "location_info": {
                    "id": "loc-uuid-123",
                    "name": "Netherlands - Amsterdam",
                    "country_code": "NL",
                    "city": "Amsterdam",
                    "flag_emoji": "🇳🇱"
                },
                "config": {
                    "dns": {
                        "servers": [
                            {
                                "tag": "cloudflare",
                                "address": "https://*******/dns-query",
                                "address_resolver": "local",
                                "detour": "proxy"
                            }
                        ],
                        "final": "cloudflare",
                        "strategy": "ipv4_only"
                    },
                    "inbounds": [
                        {
                            "type": "tun",
                            "inet4_address": "**********/30",
                            "auto_route": True,
                            "sniff": True
                        }
                    ],
                    "outbounds": [
                        {
                            "type": "selector",
                            "tag": "proxy",
                            "outbounds": ["trojan-ws", "vmess-ws", "trojan-grpc", "vmess-grpc"]
                        },
                        {
                            "type": "trojan",
                            "tag": "trojan-ws",
                            "server": "***********",
                            "server_port": 443,
                            "password": "15c175d8-703c-456a-ac82-91041f8af845",
                            "tls": {
                                "enabled": True,
                                "server_name": "***********.sslip.io"
                            },
                            "transport": {
                                "type": "ws",
                                "path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx"
                            }
                        }
                    ],
                    "route": {
                        "final": "proxy",
                        "auto_detect_interface": True
                    }
                },
                "metadata": {
                    "generated_at": "2025-06-14T19:45:00Z",
                    "config_format": "singbox_json",
                    "protocols": ["trojan", "vmess"],
                    "transports": ["websocket", "grpc"],
                    "admin_generated": True
                }
            },
            response_only=True
        ),
        OpenApiExample(
            'Error Response - User Not Found',
            description='Пользователь не найден',
            value={
                "success": False,
                "error": "User not found",
                "error_code": "USER_NOT_FOUND",
                "details": {
                    "user_id": "123e4567-e89b-12d3-a456-426614174000"
                }
            },
            response_only=True
        ),
        OpenApiExample(
            'Error Response - No Subscription',
            description='У пользователя нет активной подписки',
            value={
                "success": False,
                "error": "User has no active subscription",
                "error_code": "NO_ACTIVE_SUBSCRIPTION",
                "details": {
                    "user_id": "123e4567-e89b-12d3-a456-426614174000"
                }
            },
            response_only=True
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAdminUserOrHasAPIKey])
def generate_personalized_config(request):
    """
    Генерирует персонализированную SingBox конфигурацию для указанного пользователя.
    
    PURPOSE:
      - Предоставляет администраторам возможность генерировать конфигурации для любого пользователя
      - Обеспечивает полный контроль над процессом генерации конфигураций
      - Позволяет принудительно пересоздавать пользователей в Hiddify Manager
      - Возвращает конфигурацию в формате, идентичном singbox_Config_example
    
    AAG (Actor -> Action -> Goal):
      - Администратор -> Запрашивает конфигурацию для пользователя -> Получает готовую SingBox конфигурацию
      - Система -> Валидирует данные -> Генерирует персонализированную конфигурацию
      - Hiddify Manager -> Предоставляет данные пользователя -> Обеспечивает аутентификацию VPN
    
    CONTRACT:
      PRECONDITIONS:
        - request.user.is_staff == True (проверяется permission_classes)
        - Валидный JSON в теле запроса с user_id
        - Пользователь существует в системе
      POSTCONDITIONS:
        - Возвращается персонализированная SingBox конфигурация
        - Конфигурация соответствует формату singbox_Config_example
        - UUID пользователя подставлен во все необходимые поля
      INVARIANTS:
        - Структура конфигурации всегда соответствует базовому шаблону
        - Серверные параметры берутся из выбранной локации
        - Метаданные содержат информацию о генерации
    
    ARGS:
      - request: HTTP запрос с JSON данными
    
    RETURNS:
      - Response: JSON ответ с конфигурацией или ошибкой
    """
    try:
        # Шаг 1: Валидация входных данных
        serializer = AdminGenerateConfigRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Admin config generation: Invalid request data from {request.user.email}: {serializer.errors}")
            return Response({
                'success': False,
                'error': 'Invalid request data',
                'error_code': 'VALIDATION_ERROR',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        user_account = serializer.get_validated_user()
        force_recreate = validated_data.get('force_recreate', False)
        
        # Определяем тип аутентификации
        auth_type = "jwt_admin" if (request.user and request.user.is_authenticated) else "api_key"
        admin_identifier = request.user.email if (request.user and request.user.is_authenticated) else "api_key_user"

        logger.info(f"Admin {admin_identifier} ({auth_type}) requesting config generation for user {user_account.email} (force_recreate={force_recreate})")
        
        # Шаг 2: Проверка активной подписки
        active_subscription = ActiveSubscription.objects.filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).select_related('plan').first()
        
        if not active_subscription:
            logger.warning(f"Admin config generation: User {user_account.email} has no active subscription")
            return Response({
                'success': False,
                'error': 'User has no active subscription',
                'error_code': 'NO_ACTIVE_SUBSCRIPTION',
                'details': {
                    'user_id': str(user_account.id),
                    'user_email': user_account.email
                }
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Шаг 3: Определение локации через промежуточную модель
        from .models import SubscriptionPlanLocation

        available_plan_locations = SubscriptionPlanLocation.objects.filter(
            plan=active_subscription.plan,
            location__is_active=True
        ).select_related('location')

        if not available_plan_locations.exists():
            logger.error(f"Admin config generation: No available locations for user {user_account.email}")
            return Response({
                'success': False,
                'error': 'No available locations for user subscription',
                'error_code': 'NO_AVAILABLE_LOCATIONS',
                'details': {
                    'user_id': str(user_account.id),
                    'plan_name': active_subscription.plan.name
                }
            }, status=status.HTTP_404_NOT_FOUND)

        # Используем дефолтную локацию или первую доступную
        default_plan_location = available_plan_locations.filter(is_default=True).first()
        target_plan_location = default_plan_location or available_plan_locations.first()
        target_location = target_plan_location.location
        
        # Шаг 4: Получение или создание Hiddify пользователя
        hiddify_link = None
        hiddify_user_uuid = None
        
        try:
            hiddify_link = HiddifyLink.objects.get(
                user=user_account,
                is_active_in_hiddify=True
            )
            hiddify_user_uuid = str(hiddify_link.hiddify_user_uuid)
            
            if force_recreate:
                logger.info(f"Force recreating Hiddify user for {user_account.email}")
                # Логика принудительного пересоздания может быть добавлена здесь
                
        except HiddifyLink.DoesNotExist:
            logger.info(f"No Hiddify link found for user {user_account.email}, will use existing UUID or create new")
            # Если нет связи с Hiddify, используем ID пользователя как UUID
            hiddify_user_uuid = str(user_account.id)
        
        # Шаг 5: Генерация персонализированной конфигурации
        result = PersonalizedSingBoxService.get_config_for_existing_user(
            hiddify_user_uuid=hiddify_user_uuid,
            location_params=target_location.hiddify_params,
            user_name=user_account.email or f"user_{user_account.id}"
        )
        
        if not result['success']:
            logger.error(f"Failed to generate config for user {user_account.email}: {result.get('error')}")
            return Response({
                'success': False,
                'error': 'Failed to generate configuration',
                'error_code': 'CONFIG_GENERATION_FAILED',
                'details': {
                    'user_id': str(user_account.id),
                    'hiddify_error': result.get('error', 'Unknown error')
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Шаг 6: Подготовка ответа
        user_serializer = UserInfoSerializer(user_account)
        location_serializer = LocationInfoSerializer({
            'id': target_location.id,
            'name': target_location.name,
            'country_code': target_location.country_code,
            'city': target_location.city,
            'flag_emoji': target_location.flag_emoji,
            'is_active': target_location.is_active,
            'server_info': {
                'server': target_location.hiddify_params.get('server', 'unknown'),
                'server_port': target_location.hiddify_params.get('server_port', 443)
            }
        })
        
        response_data = {
            'success': True,
            'user_info': user_serializer.data,
            'hiddify_user_uuid': hiddify_user_uuid,
            'location_info': location_serializer.data,
            'config': result['singbox_config'],
            'metadata': {
                'generated_at': timezone.now().isoformat(),
                'generated_by_admin': admin_identifier,
                'auth_type': auth_type,
                'config_format': 'singbox_json',
                'protocols': ['trojan', 'vmess'],
                'transports': ['websocket', 'grpc', 'httpupgrade'],
                'admin_generated': True,
                'force_recreate': force_recreate
            }
        }
        
        logger.info(f"Successfully generated config for user {user_account.email} by admin {admin_identifier} ({auth_type})")
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        # Определяем admin_identifier для логирования ошибок
        try:
            admin_identifier = admin_identifier
        except NameError:
            admin_identifier = request.user.email if (request.user and request.user.is_authenticated) else "api_key_user"

        logger.error(f"Unexpected error in admin config generation by {admin_identifier}: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'error': 'Internal server error',
            'error_code': 'INTERNAL_ERROR',
            'details': {
                'message': str(e)
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['Admin'],
    summary='Simple VPN config generation with API key',
    description="""
    Упрощенный административный endpoint для быстрого создания пользователей и генерации VPN конфигураций.

    **Особенности:**
    - Простая аутентификация через API ключ (без JWT)
    - Автоматическое создание пользователей в VPN сервисе и Hiddify Manager
    - Автоматический выбор дефолтных параметров (план, локация)
    - Минимальные требования к входным данным

    **Аутентификация:**
    - API ключ: `be84eb6e-cf9d-4b2b-b063-fdf26960ebca`
    - Передача через заголовок `X-API-Key` или параметр `api_key`

    **Логика работы:**
    1. Проверяет API ключ
    2. Создает нового пользователя (если указан email) или находит существующего (если указан ID)
    3. Выбирает тарифный план (указанный или первый доступный)
    4. Выбирает локацию (по стране или первую доступную)
    5. Создает пользователя в Hiddify Manager
    6. Генерирует персонализированную SingBox конфигурацию
    7. Возвращает готовую конфигурацию с метаданными
    """,
    request=SimpleAdminConfigRequestSerializer,
    responses={
        200: SimpleAdminConfigResponseSerializer,
        400: AdminGenerateConfigErrorSerializer,
        401: AdminGenerateConfigErrorSerializer,
        403: AdminGenerateConfigErrorSerializer,
        500: AdminGenerateConfigErrorSerializer
    },
    examples=[
        OpenApiExample(
            'Create new user with email',
            description='Создание нового пользователя по email',
            value={
                "user_email": "<EMAIL>",
                "plan_name": "Premium",
                "location_country": "NL"
            },
            request_only=True
        ),
        OpenApiExample(
            'Use existing user by ID',
            description='Использование существующего пользователя по ID',
            value={
                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                "location_country": "US"
            },
            request_only=True
        ),
        OpenApiExample(
            'Minimal request with defaults',
            description='Минимальный запрос с дефолтными параметрами',
            value={
                "user_email": "<EMAIL>"
            },
            request_only=True
        )
    ]
)
@api_view(['POST'])
@permission_classes([HasValidAPIKey])
def simple_generate_config(request):
    """
    Упрощенный endpoint для генерации VPN конфигураций с автоматическим созданием пользователей.

    PURPOSE:
      - Обеспечивает простой способ создания пользователей и получения VPN конфигураций
      - Автоматизирует весь процесс от создания пользователя до готовой конфигурации
      - Использует простую аутентификацию через API ключ вместо JWT
      - Минимизирует количество требуемых параметров через умные дефолты
    """
    try:
        # Шаг 1: Валидация входных данных
        serializer = SimpleAdminConfigRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Simple admin config: Invalid request data: {serializer.errors}")
            return Response({
                'success': False,
                'error': 'Invalid request data',
                'error_code': 'VALIDATION_ERROR',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data
        user_email = validated_data.get('user_email')
        user_id = validated_data.get('user_id')
        plan_name = validated_data.get('plan_name')
        location_country = validated_data.get('location_country')

        logger.info(f"Simple admin config request: email={user_email}, user_id={user_id}, plan={plan_name}, location={location_country}")

        # Шаг 2: Получение или создание пользователя
        user_account = None
        user_created = False

        if user_id:
            # Используем существующего пользователя
            try:
                user_account = UserAccount.objects.get(id=user_id)
                logger.info(f"Found existing user: {user_account.email}")
            except UserAccount.DoesNotExist:
                logger.error(f"User with ID {user_id} not found")
                return Response({
                    'success': False,
                    'error': 'User not found',
                    'error_code': 'USER_NOT_FOUND',
                    'details': {'user_id': str(user_id)}
                }, status=status.HTTP_404_NOT_FOUND)

        elif user_email:
            # Создаем нового пользователя или находим существующего по email
            import uuid
            user_account, user_created = UserAccount.objects.get_or_create(
                email=user_email,
                defaults={
                    'username': user_email,  # Используем email как username
                    'is_anonymous': False,
                    'is_active': True
                }
            )
            if user_created:
                logger.info(f"Created new user: {user_account.email}")
            else:
                logger.info(f"Found existing user by email: {user_account.email}")

        # Шаг 3: Выбор тарифного плана
        from subscriptions.models import SubscriptionPlan, ActiveSubscription

        target_plan = None
        if plan_name:
            try:
                # Сначала пытаемся найти точное совпадение
                target_plan = SubscriptionPlan.objects.get(name__iexact=plan_name, is_active=True)
            except SubscriptionPlan.DoesNotExist:
                try:
                    # Затем ищем частичное совпадение, берем первый
                    target_plan = SubscriptionPlan.objects.filter(name__icontains=plan_name, is_active=True).first()
                    if target_plan:
                        logger.info(f"Found plan by partial match: {target_plan.name}")
                except Exception:
                    pass
            except SubscriptionPlan.MultipleObjectsReturned:
                # Если несколько планов, берем первый
                target_plan = SubscriptionPlan.objects.filter(name__icontains=plan_name, is_active=True).first()
                logger.warning(f"Multiple plans found for '{plan_name}', using first: {target_plan.name if target_plan else 'None'}")

            if not target_plan:
                logger.warning(f"Plan '{plan_name}' not found, using default")

        if not target_plan:
            target_plan = SubscriptionPlan.objects.filter(is_active=True).first()
            if not target_plan:
                logger.error("No active subscription plans found")
                return Response({
                    'success': False,
                    'error': 'No active subscription plans available',
                    'error_code': 'NO_PLANS_AVAILABLE'
                }, status=status.HTTP_404_NOT_FOUND)

        logger.info(f"Selected plan: {target_plan.name}")

        # Шаг 4: Создание активной подписки (если нужно)
        active_subscription = ActiveSubscription.objects.filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()

        subscription_created = False
        if not active_subscription:
            # Создаем новую подписку
            from datetime import timedelta
            active_subscription = ActiveSubscription.objects.create(
                user=user_account,
                plan=target_plan,
                start_date=timezone.now(),
                end_date=timezone.now() + timedelta(days=target_plan.duration_days),
                is_active=True
            )
            subscription_created = True
            logger.info(f"Created new subscription for user {user_account.email}")
        else:
            logger.info(f"Using existing subscription for user {user_account.email}")

        # Шаг 5: Выбор локации
        from .models import SubscriptionPlanLocation

        available_plan_locations = SubscriptionPlanLocation.objects.filter(
            plan=active_subscription.plan,
            location__is_active=True
        ).select_related('location')

        if not available_plan_locations.exists():
            logger.error(f"No available locations for plan {active_subscription.plan.name}")
            return Response({
                'success': False,
                'error': 'No available locations for subscription plan',
                'error_code': 'NO_AVAILABLE_LOCATIONS',
                'details': {'plan_name': active_subscription.plan.name}
            }, status=status.HTTP_404_NOT_FOUND)

        # Выбираем локацию по стране или дефолтную
        target_plan_location = None
        if location_country:
            target_plan_location = available_plan_locations.filter(
                location__country_code=location_country
            ).first()
            if not target_plan_location:
                logger.warning(f"Location for country '{location_country}' not found, using default")

        if not target_plan_location:
            # Используем дефолтную или первую доступную
            target_plan_location = available_plan_locations.filter(is_default=True).first()
            if not target_plan_location:
                target_plan_location = available_plan_locations.first()

        target_location = target_plan_location.location
        logger.info(f"Selected location: {target_location.name}")

        # Шаг 6: Создание пользователя в Hiddify Manager
        hiddify_user_created = False
        hiddify_user_uuid = None

        try:
            hiddify_link = HiddifyLink.objects.get(
                user=user_account,
                is_active_in_hiddify=True
            )
            hiddify_user_uuid = str(hiddify_link.hiddify_user_uuid)
            logger.info(f"Using existing Hiddify user: {hiddify_user_uuid}")
        except HiddifyLink.DoesNotExist:
            # Создаем нового пользователя в Hiddify
            result = PersonalizedSingBoxService.create_user_and_generate_config(
                user_name=f"{user_account.email}_{user_account.id}",
                usage_limit_gb=target_plan.traffic_limit_gb,
                package_days=target_plan.duration_days,
                location_params=target_location.hiddify_params,
                comment_data={
                    'user_id': str(user_account.id),
                    'user_email': user_account.email,
                    'plan_name': target_plan.name,
                    'created_via': 'simple_admin_api',
                    'api_key_auth': True
                }
            )

            if result['success']:
                hiddify_user_uuid = result['hiddify_user_uuid']
                hiddify_user_created = True

                # Сохраняем связь с Hiddify
                HiddifyLink.objects.create(
                    user=user_account,
                    hiddify_user_uuid=hiddify_user_uuid,
                    is_active_in_hiddify=True,
                    hiddify_created_at=timezone.now()
                )
                logger.info(f"Created new Hiddify user: {hiddify_user_uuid}")
            else:
                logger.error(f"Failed to create Hiddify user: {result.get('error')}")
                return Response({
                    'success': False,
                    'error': 'Failed to create user in Hiddify Manager',
                    'error_code': 'HIDDIFY_USER_CREATION_FAILED',
                    'details': {'hiddify_error': result.get('error')}
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Шаг 7: Генерация персонализированной конфигурации
        config_result = PersonalizedSingBoxService.get_config_for_existing_user(
            hiddify_user_uuid=hiddify_user_uuid,
            location_params=target_location.hiddify_params,
            user_name=user_account.email
        )

        if not config_result['success']:
            logger.error(f"Failed to generate config: {config_result.get('error')}")
            return Response({
                'success': False,
                'error': 'Failed to generate configuration',
                'error_code': 'CONFIG_GENERATION_FAILED',
                'details': {'config_error': config_result.get('error')}
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Шаг 8: Подготовка ответа
        response_data = {
            'success': True,
            'user_created': user_created,
            'user_info': {
                'id': str(user_account.id),
                'email': user_account.email,
                'is_anonymous': user_account.is_anonymous,
                'is_active': user_account.is_active,
                'subscription_created': subscription_created
            },
            'hiddify_user_created': hiddify_user_created,
            'hiddify_user_uuid': hiddify_user_uuid,
            'plan_info': {
                'id': str(target_plan.id),
                'name': target_plan.name,
                'duration_days': target_plan.duration_days,
                'traffic_limit_gb': target_plan.traffic_limit_gb,
                'max_devices': target_plan.max_devices
            },
            'location_info': {
                'id': str(target_location.id),
                'name': target_location.name,
                'country_code': target_location.country_code,
                'city': target_location.city,
                'flag_emoji': target_location.flag_emoji,
                'server_info': {
                    'server': target_location.hiddify_params.get('server'),
                    'server_port': target_location.hiddify_params.get('server_port')
                }
            },
            'config': config_result['singbox_config'],
            'metadata': {
                'generated_at': timezone.now().isoformat(),
                'config_format': 'singbox_json',
                'protocols': ['trojan', 'vmess'],
                'transports': ['websocket', 'grpc', 'httpupgrade'],
                'api_key_auth': True,
                'auto_created': {
                    'user': user_created,
                    'subscription': subscription_created,
                    'hiddify_user': hiddify_user_created
                }
            }
        }

        logger.info(f"Successfully generated simple config for user {user_account.email} (created: {user_created})")
        return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Unexpected error in simple admin config generation: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'error': 'Internal server error',
            'error_code': 'INTERNAL_ERROR',
            'details': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
