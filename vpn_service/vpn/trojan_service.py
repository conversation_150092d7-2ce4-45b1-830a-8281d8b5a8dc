"""
Сервис для генерации персонализированных SingBox конфигураций.
Интеграция с Hiddify Manager и шаблонами конфигураций.
"""
import logging
import json
import copy
from typing import Dict, Optional, List
from django.conf import settings
from .services import HiddifyApiService

logger = logging.getLogger(__name__)


class PersonalizedSingBoxService:
    """
    Сервис для генерации персонализированных SingBox конфигураций.

    PURPOSE:
      - Объединяет базовый шаблон SingBox с данными пользователя из Hiddify Manager
      - Создает уникальные конфигурации для каждого пользователя ("SIM-карта пользователя")
      - Поддерживает множественные протоколы (Trojan, VMess) и транспорты (WebSocket, gRPC)
      - Интегрируется с системой локаций для выбора серверов

    AAG (Actor -> Action -> Goal):
      - VPN Service -> Запрашивает конфигурацию -> Получает готовую SingBox конфигурацию
      - Сервис -> Комбинирует шаблон + данные пользователя -> Возвращает персонализированную конфигурацию
      - Пользователь -> Импортирует конфигурацию в SingBox -> Подключается к VPN

    CONTRACT:
      PRECONDITIONS:
        - Базовый шаблон SingBox конфигурации доступен
        - Пользователь создан в Hiddify Manager
        - Локация содержит корректные hiddify_params
      POSTCONDITIONS:
        - Возвращается валидная SingBox JSON конфигурация
        - Все параметры пользователя корректно подставлены
        - Конфигурация готова к использованию в SingBox клиенте
      INVARIANTS:
        - UUID пользователя используется как password/uuid в outbound'ах
        - Серверные параметры берутся из локации
        - Базовая структура конфигурации сохраняется
    """

    @staticmethod
    def load_base_template() -> Dict:
        """
        Загружает базовый шаблон SingBox конфигурации.

        PURPOSE:
          - Предоставляет базовую структуру конфигурации
          - Содержит все необходимые секции (dns, inbounds, outbounds, route)
          - Служит основой для персонализации

        RETURNS:
          - Dict: Базовая SingBox конфигурация
        """
        # Базовый шаблон на основе singbox_Config_example
        base_template = {
            "dns": {
                "servers": [
                    {
                        "tag": "cloudflare",
                        "address": "https://*******/dns-query",
                        "address_resolver": "local",
                        "detour": "proxy"
                    },
                    {
                        "tag": "cloudflare-tls",
                        "address": "tls://*******",
                        "address_resolver": "local",
                        "detour": "proxy"
                    },
                    {
                        "tag": "google",
                        "address": "tls://*******",
                        "address_resolver": "local",
                        "detour": "proxy"
                    },
                    {
                        "tag": "local",
                        "address": "*********",
                        "detour": "direct"
                    },
                    {
                        "tag": "block",
                        "address": "rcode://success"
                    }
                ],
                "rules": [
                    {
                        "outbound": "any",
                        "server": "local"
                    },
                    {
                        "geoip": ["private"],
                        "server": "local"
                    },
                    {
                        "geoip": ["cn"],
                        "server": "local"
                    },
                    {
                        "domain_suffix": [".cn", ".ru"],
                        "server": "local"
                    }
                ],
                "final": "cloudflare",
                "strategy": "ipv4_only",
                "disable_cache": False,
                "disable_expire": False
            },
            "inbounds": [
                {
                    "type": "tun",
                    "inet4_address": "**********/30",
                    "inet6_address": "fdfe:dcba:9876::1/126",
                    "auto_route": True,
                    "strict_route": False,
                    "sniff": True,
                    "sniff_override_destination": False,
                    "domain_strategy": "ipv4_only"
                }
            ],
            "outbounds": [
                {
                    "type": "selector",
                    "tag": "proxy",
                    "outbounds": [
                        "trojan-ws",
                        "vmess-ws",
                        "vmess-httpupgrade",
                        "trojan-grpc",
                        "vmess-grpc"
                    ]
                },
                # Placeholder outbounds - будут заменены персонализированными
                {},  # trojan-ws
                {},  # vmess-ws
                {},  # vmess-httpupgrade
                {},  # trojan-grpc
                {},  # vmess-grpc
                {
                    "type": "direct",
                    "tag": "direct"
                },
                {
                    "type": "block",
                    "tag": "block"
                },
                {
                    "type": "dns",
                    "tag": "dns-out"
                }
            ],
            "route": {
                "rules": [
                    {
                        "protocol": "dns",
                        "outbound": "dns-out"
                    },
                    {
                        "geoip": ["private"],
                        "outbound": "direct"
                    },
                    {
                        "geoip": ["cn"],
                        "outbound": "direct"
                    },
                    {
                        "domain_suffix": [".cn", ".ru"],
                        "outbound": "direct"
                    }
                ],
                "final": "proxy",
                "auto_detect_interface": True
            }
        }

        logger.info("Loaded base SingBox template")
        return base_template

    @staticmethod
    def generate_personalized_config(
        hiddify_user_uuid: str,
        location_params: Dict,
        user_name: str = None
    ) -> Dict:
        """
        Генерирует персонализированную SingBox конфигурацию для пользователя.

        PURPOSE:
          - Создает уникальную "SIM-карту пользователя" - персонализированную VPN конфигурацию
          - Объединяет базовый шаблон с параметрами локации и данными пользователя
          - Подставляет UUID пользователя во все outbound'ы для аутентификации
          - Настраивает серверные параметры на основе выбранной локации

        ARGS:
          - hiddify_user_uuid (str): UUID пользователя в Hiddify Manager
          - location_params (Dict): Параметры локации из hiddify_params
          - user_name (str, optional): Имя пользователя для логирования

        RETURNS:
          - Dict: Персонализированная SingBox JSON конфигурация

        EXAMPLE location_params:
          {
            "server": "***********",
            "server_port": 443,
            "tls_server_name": "***********.sslip.io",
            "trojan_ws_path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx",
            "vmess_ws_path": "/39m0pgSOrY19tjCyr3egnx",
            "vmess_httpupgrade_path": "/39m0pgSOdKbicJLIaR",
            "trojan_grpc_service": "Cgm6B1DqLOKIFOh7gdS9",
            "vmess_grpc_service": "39m0pgSOOh7gdS9"
          }
        """

        # Шаг 1: Загружаем базовый шаблон
        config = copy.deepcopy(PersonalizedSingBoxService.load_base_template())

        # Шаг 2: Извлекаем параметры локации с fallback значениями
        server = location_params.get('server', '***********')
        server_port = location_params.get('server_port', 443)
        tls_server_name = location_params.get('tls_server_name', f"{server}.sslip.io")

        # Пути для различных транспортов
        trojan_ws_path = location_params.get('trojan_ws_path', '/Cgm6B1DqLOKIFrY19tjCyr3egnx')
        vmess_ws_path = location_params.get('vmess_ws_path', '/39m0pgSOrY19tjCyr3egnx')
        vmess_httpupgrade_path = location_params.get('vmess_httpupgrade_path', '/39m0pgSOdKbicJLIaR')
        trojan_grpc_service = location_params.get('trojan_grpc_service', 'Cgm6B1DqLOKIFOh7gdS9')
        vmess_grpc_service = location_params.get('vmess_grpc_service', '39m0pgSOOh7gdS9')

        # Шаг 3: Создаем персонализированные outbound'ы
        personalized_outbounds = [
            # Selector остается без изменений
            config["outbounds"][0],

            # Trojan WebSocket
            {
                "type": "trojan",
                "tag": "trojan-ws",
                "server": server,
                "server_port": server_port,
                "password": hiddify_user_uuid,
                "tls": {
                    "enabled": True,
                    "server_name": tls_server_name,
                    "alpn": "http/1.1",
                    "utls": {
                        "enabled": True,
                        "fingerprint": "chrome"
                    }
                },
                "transport": {
                    "type": "ws",
                    "path": trojan_ws_path,
                    "headers": {
                        "Host": tls_server_name
                    },
                    "early_data_header_name": "Sec-WebSocket-Protocol"
                }
            },

            # VMess WebSocket
            {
                "type": "vmess",
                "tag": "vmess-ws",
                "server": server,
                "server_port": server_port,
                "uuid": hiddify_user_uuid,
                "security": "auto",
                "tls": {
                    "enabled": True,
                    "server_name": tls_server_name,
                    "alpn": "http/1.1",
                    "utls": {
                        "enabled": True,
                        "fingerprint": "chrome"
                    }
                },
                "packet_encoding": "xudp",
                "transport": {
                    "type": "ws",
                    "path": vmess_ws_path,
                    "headers": {
                        "Host": tls_server_name
                    },
                    "early_data_header_name": "Sec-WebSocket-Protocol"
                }
            },

            # VMess HTTP Upgrade
            {
                "type": "vmess",
                "tag": "vmess-httpupgrade",
                "server": server,
                "server_port": server_port,
                "uuid": hiddify_user_uuid,
                "security": "auto",
                "tls": {
                    "enabled": True,
                    "server_name": tls_server_name,
                    "alpn": "http/1.1",
                    "utls": {
                        "enabled": True,
                        "fingerprint": "chrome"
                    }
                },
                "packet_encoding": "xudp",
                "transport": {
                    "type": "httpupgrade",
                    "path": vmess_httpupgrade_path,
                    "headers": {
                        "Host": tls_server_name
                    }
                }
            },

            # Trojan gRPC
            {
                "type": "trojan",
                "tag": "trojan-grpc",
                "server": server,
                "server_port": server_port,
                "password": hiddify_user_uuid,
                "tls": {
                    "enabled": True,
                    "server_name": tls_server_name,
                    "alpn": "h2",
                    "utls": {
                        "enabled": True,
                        "fingerprint": "chrome"
                    }
                },
                "transport": {
                    "type": "grpc",
                    "service_name": trojan_grpc_service,
                    "idle_timeout": "1m55s",
                    "ping_timeout": "15s"
                }
            },

            # VMess gRPC
            {
                "type": "vmess",
                "tag": "vmess-grpc",
                "server": server,
                "server_port": server_port,
                "uuid": hiddify_user_uuid,
                "security": "auto",
                "tls": {
                    "enabled": True,
                    "server_name": tls_server_name,
                    "alpn": "h2",
                    "utls": {
                        "enabled": True,
                        "fingerprint": "chrome"
                    }
                },
                "packet_encoding": "xudp",
                "transport": {
                    "type": "grpc",
                    "service_name": vmess_grpc_service,
                    "idle_timeout": "1m55s",
                    "ping_timeout": "15s"
                }
            }
        ]

        # Добавляем системные outbound'ы (direct, block, dns-out)
        personalized_outbounds.extend(config["outbounds"][-3:])

        # Шаг 4: Заменяем outbound'ы в конфигурации
        config["outbounds"] = personalized_outbounds

        # Шаг 5: Логирование и возврат результата
        user_display = user_name or hiddify_user_uuid[:8]
        logger.info(f"Generated personalized SingBox config for user {user_display} on server {server}")
        logger.info(f"Config includes {len(personalized_outbounds)-4} protocol variants (Trojan+VMess with WS/gRPC)")

        return config

    @staticmethod
    def create_user_and_generate_config(
        user_name: str,
        usage_limit_gb: int,
        package_days: int,
        location_params: Dict,
        comment_data: Dict = None
    ) -> Dict:
        """
        Создает пользователя в Hiddify Manager и генерирует персонализированную конфигурацию.

        PURPOSE:
          - Полный цикл создания VPN пользователя: от регистрации до готовой конфигурации
          - Интегрирует создание пользователя в Hiddify с генерацией SingBox конфигурации
          - Обеспечивает атомарность операции: либо все успешно, либо откат

        ARGS:
          - user_name (str): Имя пользователя в Hiddify
          - usage_limit_gb (int): Лимит трафика в ГБ
          - package_days (int): Срок действия в днях
          - location_params (Dict): Параметры локации для конфигурации
          - comment_data (Dict, optional): Дополнительные метаданные

        RETURNS:
          - Dict: {
              'success': bool,
              'hiddify_user_uuid': str,
              'singbox_config': dict,
              'error': str (если success=False)
            }
        """
        try:
            # Шаг 1: Создаем пользователя в Hiddify Manager
            hiddify_service = HiddifyApiService()

            # Подготавливаем комментарий
            if comment_data is None:
                comment_data = {}

            comment_data.update({
                'created_by': 'PersonalizedSingBoxService',
                'location_server': location_params.get('server', 'unknown'),
                'config_type': 'personalized_singbox'
            })

            comment_json_string = json.dumps(comment_data)

            # Создаем пользователя
            success, hiddify_response = hiddify_service.create_hiddify_user(
                name=user_name,
                usage_limit_gb=usage_limit_gb,
                package_days=package_days,
                comment_json_string=comment_json_string
            )

            if not success:
                logger.error(f"Failed to create Hiddify user {user_name}: {hiddify_response}")
                return {
                    'success': False,
                    'error': f"Failed to create user in Hiddify Manager: {hiddify_response}"
                }

            hiddify_user_uuid = hiddify_response['uuid']
            logger.info(f"Created Hiddify user {user_name} with UUID {hiddify_user_uuid}")

            # Шаг 2: Генерируем персонализированную конфигурацию
            singbox_config = PersonalizedSingBoxService.generate_personalized_config(
                hiddify_user_uuid=hiddify_user_uuid,
                location_params=location_params,
                user_name=user_name
            )

            logger.info(f"Successfully created user and config for {user_name}")

            return {
                'success': True,
                'hiddify_user_uuid': hiddify_user_uuid,
                'singbox_config': singbox_config
            }

        except Exception as e:
            logger.error(f"Error in create_user_and_generate_config for {user_name}: {str(e)}")
            return {
                'success': False,
                'error': f"Internal error: {str(e)}"
            }

    @staticmethod
    def get_config_for_existing_user(
        hiddify_user_uuid: str,
        location_params: Dict,
        user_name: str = None
    ) -> Dict:
        """
        Генерирует конфигурацию для существующего пользователя Hiddify.

        PURPOSE:
          - Создает новую конфигурацию для уже зарегистрированного пользователя
          - Позволяет менять локации без пересоздания пользователя
          - Обновляет конфигурацию при изменении параметров сервера

        ARGS:
          - hiddify_user_uuid (str): UUID существующего пользователя
          - location_params (Dict): Параметры локации
          - user_name (str, optional): Имя для логирования

        RETURNS:
          - Dict: {
              'success': bool,
              'singbox_config': dict,
              'user_info': dict (если доступно),
              'error': str (если success=False)
            }
        """
        try:
            # Шаг 1: Проверяем существование пользователя (опционально)
            hiddify_service = HiddifyApiService()
            user_exists, user_info = hiddify_service.get_hiddify_user_info(hiddify_user_uuid)

            if not user_exists:
                logger.warning(f"User {hiddify_user_uuid} not found in Hiddify, but generating config anyway")
                user_info = {}

            # Шаг 2: Генерируем конфигурацию
            singbox_config = PersonalizedSingBoxService.generate_personalized_config(
                hiddify_user_uuid=hiddify_user_uuid,
                location_params=location_params,
                user_name=user_name
            )

            logger.info(f"Generated config for existing user {hiddify_user_uuid[:8]}")

            return {
                'success': True,
                'singbox_config': singbox_config,
                'user_info': user_info
            }

        except Exception as e:
            logger.error(f"Error generating config for user {hiddify_user_uuid}: {str(e)}")
            return {
                'success': False,
                'error': f"Failed to generate config: {str(e)}"
            }

    # Сохраняем старые методы для обратной совместимости
    @staticmethod
    def generate_trojan_tun_config(
        server: str = "ductuspro.ru",
        server_port: int = 443,
        password: str = None,
        ws_path: str = None,
        user_uuid: str = None
    ) -> Dict:
        """
        DEPRECATED: Используйте generate_personalized_config вместо этого метода.
        Оставлен для обратной совместимости.
        """
        logger.warning("generate_trojan_tun_config is deprecated, use generate_personalized_config")

        # Конвертируем в новый формат
        location_params = {
            'server': server,
            'server_port': server_port,
            'trojan_ws_path': ws_path or '/Cgm6B1DqLOKIFrY19tjCyr3egnx'
        }

        return PersonalizedSingBoxService.generate_personalized_config(
            hiddify_user_uuid=password or "15c175d8-703c-456a-ac82-91041f8af845",
            location_params=location_params,
            user_name=user_uuid
        )

    @staticmethod
    def generate_trojan_mixed_config(
        server: str = "ductuspro.ru",
        server_port: int = 443,
        password: str = None,
        ws_path: str = None,
        listen_port: int = 2080,
        user_uuid: str = None
    ) -> Dict:
        """
        DEPRECATED: Используйте generate_personalized_config для полной функциональности.
        Генерирует упрощенную Trojan конфигурацию с Mixed интерфейсом (HTTP/SOCKS прокси).
        """
        logger.warning("generate_trojan_mixed_config is deprecated, consider using generate_personalized_config")

        if not password:
            password = "15c175d8-703c-456a-ac82-91041f8af845"

        if not ws_path:
            ws_path = "/Cgm6B1DqLOKIFrY19tjCyr3egnx"

        config = {
            "log": {
                "level": "info",
                "timestamp": True
            },
            "dns": {
                "servers": [
                    {
                        "tag": "google",
                        "address": "*******"
                    },
                    {
                        "tag": "local",
                        "address": "local",
                        "detour": "direct"
                    }
                ],
                "rules": [
                    {
                        "outbound": "any",
                        "server": "local"
                    }
                ]
            },
            "inbounds": [
                {
                    "type": "mixed",
                    "tag": "mixed-in",
                    "listen": "127.0.0.1",
                    "listen_port": listen_port
                }
            ],
            "outbounds": [
                {
                    "type": "trojan",
                    "tag": "trojan-ws",
                    "server": server,
                    "server_port": server_port,
                    "password": password,
                    "transport": {
                        "type": "ws",
                        "path": ws_path,
                        "headers": {
                            "Host": server
                        }
                    },
                    "tls": {
                        "enabled": True,
                        "server_name": server,
                        "insecure": False,
                        "alpn": ["h2", "http/1.1"]
                    }
                },
                {
                    "type": "direct",
                    "tag": "direct"
                },
                {
                    "type": "block",
                    "tag": "block"
                }
            ],
            "route": {
                "rules": [
                    {
                        "ip_is_private": True,
                        "outbound": "direct"
                    }
                ],
                "final": "trojan-ws",
                "auto_detect_interface": True
            }
        }

        logger.info(f"Generated Trojan Mixed config for user {user_uuid} on port {listen_port}")
        return config

    @staticmethod
    def get_connection_info(location_params: Dict = None) -> Dict:
        """
        Возвращает информацию о подключении для указанной локации.

        PURPOSE:
          - Предоставляет метаданные о доступных протоколах и параметрах
          - Используется для отображения информации в UI
          - Помогает в диагностике подключений

        ARGS:
          - location_params (Dict, optional): Параметры локации

        RETURNS:
          - Dict: Информация о доступных протоколах и параметрах
        """
        if location_params is None:
            location_params = {
                'server': 'ductuspro.ru',
                'server_port': 443
            }

        return {
            "protocols": ["trojan", "vmess"],
            "transports": ["websocket", "grpc", "httpupgrade"],
            "server": location_params.get('server', 'ductuspro.ru'),
            "server_port": location_params.get('server_port', 443),
            "tls_enabled": True,
            "tls_server_name": location_params.get('tls_server_name', f"{location_params.get('server', 'ductuspro.ru')}.sslip.io"),
            "supported_clients": ["SingBox", "v2rayN", "Clash"],
            "config_format": "singbox_json"
        }

    @staticmethod
    def get_trojan_connection_info() -> Dict:
        """
        DEPRECATED: Используйте get_connection_info() для получения полной информации.
        Оставлен для обратной совместимости.
        """
        logger.warning("get_trojan_connection_info is deprecated, use get_connection_info")
        return {
            "protocol": "trojan",
            "server": "ductuspro.ru",
            "server_port": 443,
            "transport": "websocket",
            "ws_path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx",
            "tls_enabled": True,
            "server_name": "ductuspro.ru",
            "alpn": ["h2", "http/1.1"]
        }


# Алиас для обратной совместимости
TrojanConfigService = PersonalizedSingBoxService
