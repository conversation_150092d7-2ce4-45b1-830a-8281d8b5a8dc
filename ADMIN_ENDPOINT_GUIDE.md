# Руководство по использованию административного endpoint для генерации SingBox конфигураций

## Быстрый старт

### 1. Получение доступа

Для использования административного endpoint вам необходимо:
- Учетная запись администратора (`is_staff = True`)
- JWT токен для авторизации

### 2. Получение JWT токена

```bash
curl -X POST "http://ductuspro.ru:8090/api/auth/login/" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_admin_password"
  }'
```

Ответ:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 3. Генерация конфигурации

```bash
curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/generate-personalized-config/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-************",
    "force_recreate": false
  }'
```

## Swagger UI

### Доступ к документации

Откройте в браузере: **http://ductuspro.ru:8090/api/docs/**

### Использование через Swagger UI

1. **Авторизация**:
   - Нажмите кнопку "Authorize" в правом верхнем углу
   - Введите: `Bearer YOUR_JWT_TOKEN`
   - Нажмите "Authorize"

2. **Найдите endpoint**:
   - Раздел "Admin"
   - `POST /api/vpn/admin/generate-personalized-config/`

3. **Выполните запрос**:
   - Нажмите "Try it out"
   - Заполните параметры
   - Нажмите "Execute"

## Параметры запроса

### user_id (обязательно)
- **Тип**: UUID
- **Описание**: Идентификатор пользователя в системе
- **Пример**: `"123e4567-e89b-12d3-a456-************"`

### force_recreate (опционально)
- **Тип**: boolean
- **Описание**: Принудительно пересоздать пользователя в Hiddify Manager
- **По умолчанию**: `false`
- **Использование**: Установите `true` если нужно обновить данные пользователя в Hiddify

## Структура ответа

### Успешный ответ

```json
{
  "success": true,
  "user_info": {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "active_subscription": {
      "plan_name": "Premium Monthly",
      "expires_at": "2025-07-14T12:00:00Z"
    }
  },
  "hiddify_user_uuid": "5b7414cf-b91b-40bc-b65d-ffcc017f4255",
  "location_info": {
    "name": "Netherlands - Amsterdam",
    "country_code": "NL"
  },
  "config": {
    // Полная SingBox конфигурация
  },
  "metadata": {
    "generated_at": "2025-06-14T16:44:49Z",
    "generated_by_admin": "<EMAIL>"
  }
}
```

### Использование конфигурации

Полученную конфигурацию можно:
1. **Сохранить в файл** с расширением `.json`
2. **Отправить пользователю** для импорта в SingBox
3. **Использовать для диагностики** проблем подключения

## Коды ошибок

### 400 Bad Request
```json
{
  "success": false,
  "error": "Invalid request data",
  "error_code": "VALIDATION_ERROR"
}
```
**Причины**: Неверный формат user_id или отсутствующие параметры

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```
**Причины**: Отсутствует JWT токен или токен недействителен

### 403 Forbidden
```json
{
  "detail": "You do not have permission to perform this action."
}
```
**Причины**: Пользователь не является администратором

### 404 Not Found
```json
{
  "success": false,
  "error": "User has no active subscription",
  "error_code": "NO_ACTIVE_SUBSCRIPTION"
}
```
**Причины**: Пользователь не найден или нет активной подписки

## Практические примеры

### Пример 1: Генерация конфигурации для пользователя

```bash
# Получаем список пользователей (через Django admin или API)
# Выбираем user_id нужного пользователя

curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/generate-personalized-config/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -d '{
    "user_id": "295b8372-4988-4058-addb-945a855d8845",
    "force_recreate": false
  }' | jq '.'
```

### Пример 2: Сохранение конфигурации в файл

```bash
curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/generate-personalized-config/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": "295b8372-4988-4058-addb-945a855d8845"
  }' | jq '.config' > user_config.json
```

### Пример 3: Принудительное пересоздание пользователя

```bash
curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/generate-personalized-config/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": "295b8372-4988-4058-addb-945a855d8845",
    "force_recreate": true
  }'
```

## Диагностика проблем

### Проблема: "Authentication credentials were not provided"
**Решение**: Проверьте заголовок Authorization:
```bash
-H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Проблема: "You do not have permission"
**Решение**: Убедитесь, что ваша учетная запись имеет права администратора

### Проблема: "User has no active subscription"
**Решение**: 
1. Проверьте, что пользователь существует
2. Убедитесь, что у пользователя есть активная подписка
3. Проверьте срок действия подписки

### Проблема: "No available locations"
**Решение**:
1. Проверьте, что в плане подписки настроены локации
2. Убедитесь, что локации активны
3. Проверьте связи план-локация в админ панели

## Мониторинг и логи

### Логи операций

Все операции логируются в Django логи:
```
<NAME_EMAIL> requesting config generation <NAME_EMAIL>
INFO Successfully generated config <NAME_EMAIL> <NAME_EMAIL>
```

### Метрики

Рекомендуется отслеживать:
- Количество запросов от администраторов
- Время генерации конфигураций
- Ошибки авторизации и валидации
- Успешность создания пользователей в Hiddify

## Безопасность

### Рекомендации

1. **Используйте HTTPS** в продакшене
2. **Регулярно обновляйте JWT токены**
3. **Ограничьте доступ** только необходимым администраторам
4. **Мониторьте логи** на предмет подозрительной активности
5. **Не передавайте токены** через незащищенные каналы

### Срок действия токенов

- **Access токен**: 1 час (по умолчанию)
- **Refresh токен**: 7 дней (по умолчанию)

Обновление токена:
```bash
curl -X POST "http://ductuspro.ru:8090/api/auth/token/refresh/" \
  -H "Content-Type: application/json" \
  -d '{"refresh": "YOUR_REFRESH_TOKEN"}'
```

## Поддержка

При возникновении проблем:
1. Проверьте логи Django сервера
2. Убедитесь в корректности JWT токена
3. Проверьте права доступа пользователя
4. Обратитесь к технической документации

**Техническая документация**: `technical_documentation/admin_endpoint_implementation.md`
