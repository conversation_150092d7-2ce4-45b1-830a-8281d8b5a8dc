<body>

  <elements-api apidescriptionurl="/KoijrWEEaRmhMwJ1axgUTAVuL37H/api/openapi.json" layout="sidebar" router="hash"><div style="height: 100%;"><div class="" id="mosaic-provider-react-aria-0-1" style="height: 100%;"><div data-overlay-container="true" class="" style="height: 100%;"><div class="sl-elements sl-antialiased sl-h-full sl-text-base sl-font-ui sl-text-body"><div class="sl-elements-api sl-flex sl-inset-0 sl-h-full"><div class="sl-flex" style="max-width: 450px;"><div class="sl-flex sl-overflow-y-auto sl-flex-col sl-sticky sl-inset-y-0 sl-pt-8 sl-bg-canvas-100 sl-border-r" style="padding-left: calc(50% - 900px); width: 300px; min-width: 300px;"><div class="sl-flex sl-items-center sl-mb-5 sl-ml-4"><h4 class="sl-text-paragraph sl-leading-snug sl-font-prose sl-font-semibold sl-text-heading">Hiddify API</h4></div><div class="sl-flex sl-overflow-y-auto sl-flex-col sl-flex-grow sl-flex-shrink"><div class="sl-overflow-y-auto sl-w-full sl-bg-canvas-100"><div class="sl-my-3"><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/"><div id="sl-toc-/" title="Overview" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate">Overview</div><div class="sl-flex sl-items-center sl-text-xs"></div></div></a><div class="sl-text-sm sl-leading-relaxed sl-tracking-wide sl-font-bold sl-uppercase sl-mt-6 sl-mb-2 sl-pl-4">Endpoints</div><div title="Api_Admin" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5">Api_Admin</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" class="svg-inline--fa fa-chevron-down fa-fw sl-icon sl-text-muted" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M224 416c-8.188 0-16.38-3.125-22.62-9.375l-192-192c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L224 338.8l169.4-169.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-192 192C240.4 412.9 232.2 416 224 416z"></path></svg></div></div></div><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-admin_user/get"><div id="sl-toc-/paths/proxy_path--api-v2-admin-admin_user/get" title="Admin: Get all admins" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Admin: Get all admins</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-success">get</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-admin_user/post"><div id="sl-toc-/paths/proxy_path--api-v2-admin-admin_user/post" title="Admin: Create an admin" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Admin: Create an admin</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-primary">post</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-admin_user--uuid-/delete"><div id="sl-toc-/paths/proxy_path--api-v2-admin-admin_user--uuid-/delete" title="Admin: Delete an admin" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Admin: Delete an admin</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-danger">delete</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-admin_user--uuid-/get"><div id="sl-toc-/paths/proxy_path--api-v2-admin-admin_user--uuid-/get" title="Admin: Get an admin" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Admin: Get an admin</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-success">get</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-admin_user--uuid-/patch"><div id="sl-toc-/paths/proxy_path--api-v2-admin-admin_user--uuid-/patch" title="Admin: Update an admin" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Admin: Update an admin</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-warning">patch</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-all-configs/get"><div id="sl-toc-/paths/proxy_path--api-v2-admin-all-configs/get" title="System: All Configs for configuration" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">System: All Configs for configuration</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-success">get</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-log/post"><div id="sl-toc-/paths/proxy_path--api-v2-admin-log/post" title="System: View Log file" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">System: View Log file</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-primary">post</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-me/get"><div id="sl-toc-/paths/proxy_path--api-v2-admin-me/get" title="Current Admin Info" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Current Admin Info</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-success">get</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-server_status/get"><div id="sl-toc-/paths/proxy_path--api-v2-admin-server_status/get" title="System: ServerStatus" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">System: ServerStatus</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-success">get</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-update_user_usage/get"><div id="sl-toc-/paths/proxy_path--api-v2-admin-update_user_usage/get" title="System: Update User Usage" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">System: Update User Usage</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-success">get</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-user/get"><div id="sl-toc-/paths/proxy_path--api-v2-admin-user/get" title="User: List users of current admin" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">User: List users of current admin</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-success">get</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-user/post"><div id="sl-toc-/paths/proxy_path--api-v2-admin-user/post" title="User: Create a user" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-primary-tint sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">User: Create a user</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-primary">post</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-user--uuid-/delete"><div id="sl-toc-/paths/proxy_path--api-v2-admin-user--uuid-/delete" title="User: Delete a User" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">User: Delete a User</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-danger">delete</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-user--uuid-/get"><div id="sl-toc-/paths/proxy_path--api-v2-admin-user--uuid-/get" title="User: Get details of a user" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">User: Get details of a user</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-success">get</div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/paths/proxy_path--api-v2-admin-user--uuid-/patch"><div id="sl-toc-/paths/proxy_path--api-v2-admin-user--uuid-/patch" title="User: Update a user" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-8 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bullseye" class="svg-inline--fa fa-bullseye sl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="color: rgb(151, 71, 255);"><path fill="currentColor" d="M288 256C288 273.7 273.7 288 256 288C238.3 288 224 273.7 224 256C224 238.3 238.3 224 256 224C273.7 224 288 238.3 288 256zM112 256C112 176.5 176.5 112 256 112C335.5 112 400 176.5 400 256C400 335.5 335.5 400 256 400C176.5 400 112 335.5 112 256zM256 336C300.2 336 336 300.2 336 256C336 211.8 300.2 176 256 176C211.8 176 176 211.8 176 256C176 300.2 211.8 336 256 336zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 64C149.1 64 64 149.1 64 256C64 362 149.1 448 256 448C362 448 448 362 448 256C448 149.1 362 64 256 64z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">User: Update a user</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-font-medium sl-uppercase sl-text-warning">patch</div></div></div></a><div title="Api_User" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5">Api_User</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" class="svg-inline--fa fa-chevron-right fa-fw sl-icon sl-text-muted" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M96 480c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L242.8 256L73.38 86.63c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l192 192c12.5 12.5 12.5 32.75 0 45.25l-192 192C112.4 476.9 104.2 480 96 480z"></path></svg></div></div></div><div title="Api_User_By_Uuid" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5">Api_User_By_Uuid</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" class="svg-inline--fa fa-chevron-right fa-fw sl-icon sl-text-muted" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M96 480c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L242.8 256L73.38 86.63c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l192 192c12.5 12.5 12.5 32.75 0 45.25l-192 192C112.4 476.9 104.2 480 96 480z"></path></svg></div></div></div><div title="Api_Panel" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5">Api_Panel</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" class="svg-inline--fa fa-chevron-right fa-fw sl-icon sl-text-muted" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M96 480c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L242.8 256L73.38 86.63c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l192 192c12.5 12.5 12.5 32.75 0 45.25l-192 192C112.4 476.9 104.2 480 96 480z"></path></svg></div></div></div><div class="sl-text-sm sl-leading-relaxed sl-tracking-wide sl-font-bold sl-uppercase sl-mt-6 sl-mb-2 sl-pl-4">Schemas</div><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/Admin"><div id="sl-toc-/schemas/Admin" title="Admin" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Admin</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/AdminInputLogfile"><div id="sl-toc-/schemas/AdminInputLogfile" title="AdminInputLogfile" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">AdminInputLogfile</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/App"><div id="sl-toc-/schemas/App" title="App" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">App</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/AppInstall"><div id="sl-toc-/schemas/AppInstall" title="AppInstall" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">AppInstall</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/Config"><div id="sl-toc-/schemas/Config" title="Config" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Config</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/HTTPError"><div id="sl-toc-/schemas/HTTPError" title="HTTPError" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">HTTPError</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/Mtproxy"><div id="sl-toc-/schemas/Mtproxy" title="Mtproxy" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Mtproxy</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/PanelInfoOutput"><div id="sl-toc-/schemas/PanelInfoOutput" title="PanelInfoOutput" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">PanelInfoOutput</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/PatchAdmin"><div id="sl-toc-/schemas/PatchAdmin" title="PatchAdmin" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">PatchAdmin</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/PatchUser"><div id="sl-toc-/schemas/PatchUser" title="PatchUser" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">PatchUser</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/PongOutput"><div id="sl-toc-/schemas/PongOutput" title="PongOutput" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">PongOutput</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/PostUser"><div id="sl-toc-/schemas/PostUser" title="PostUser" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">PostUser</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/Profile"><div id="sl-toc-/schemas/Profile" title="Profile" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Profile</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/ServerStatusOutput"><div id="sl-toc-/schemas/ServerStatusOutput" title="ServerStatusOutput" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">ServerStatusOutput</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/Short"><div id="sl-toc-/schemas/Short" title="Short" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Short</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/Successful"><div id="sl-toc-/schemas/Successful" title="Successful" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">Successful</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/User"><div id="sl-toc-/schemas/User" title="User" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">User</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/UserInfoChangable"><div id="sl-toc-/schemas/UserInfoChangable" title="UserInfoChangable" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">UserInfoChangable</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a><a class="ElementsTableOfContentsItem sl-block sl-no-underline" href="#/schemas/ValidationError"><div id="sl-toc-/schemas/ValidationError" title="ValidationError" class="sl-flex sl-items-center sl-h-md sl-pr-4 sl-pl-4 sl-bg-canvas-100 hover:sl-bg-canvas-200 sl-cursor-pointer sl-select-none"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cube" class="svg-inline--fa fa-cube sl-icon sl-text-warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M234.5 5.709C248.4 .7377 263.6 .7377 277.5 5.709L469.5 74.28C494.1 83.38 512 107.5 512 134.6V377.4C512 404.5 494.1 428.6 469.5 437.7L277.5 506.3C263.6 511.3 248.4 511.3 234.5 506.3L42.47 437.7C17 428.6 0 404.5 0 377.4V134.6C0 107.5 17 83.38 42.47 74.28L234.5 5.709zM256 65.98L82.34 128L256 190L429.7 128L256 65.98zM288 434.6L448 377.4V189.4L288 246.6V434.6z"></path></svg><div class="sl-flex-1 sl-items-center sl-text-base sl-truncate sl-mr-1.5 sl-ml-1.5">ValidationError</div><div class="sl-flex sl-items-center sl-text-xs"><div class="sl-flex sl-items-center"></div></div></div></a></div></div></div><a href="https://stoplight.io/?utm_source=elements&amp;utm_medium=Hiddify API&amp;utm_campaign=powered_by&amp;utm_content=/paths/proxy_path--api-v2-admin-user/post" target="_blank" rel="noopener noreferrer" class="sl-flex sl-items-center sl-px-4 sl-py-3 sl-border-t"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bolt" class="svg-inline--fa fa-bolt sl-icon fa-fw sl-mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" style="color: rgb(144, 97, 249);"><path fill="currentColor" d="M240.5 224H352C365.3 224 377.3 232.3 381.1 244.7C386.6 257.2 383.1 271.3 373.1 280.1L117.1 504.1C105.8 513.9 89.27 514.7 77.19 505.9C65.1 497.1 60.7 481.1 66.59 467.4L143.5 288H31.1C18.67 288 6.733 279.7 2.044 267.3C-2.645 254.8 .8944 240.7 10.93 231.9L266.9 7.918C278.2-1.92 294.7-2.669 306.8 6.114C318.9 14.9 323.3 30.87 317.4 44.61L240.5 224z"></path></svg><div>powered by&nbsp;<strong>Stoplight</strong></div></a></div><div class="sl-flex sl-flex-grow-0 sl-flex-shrink-0 sl-justify-self-end sl-resize-x" style="width: 1em; flex-basis: 6px; cursor: ew-resize;"></div></div><div class="sl-overflow-y-auto sl-flex-1 sl-w-full sl-px-24 sl-bg-canvas"><div class="sl-py-16" style="max-width: 1500px;"><div class="sl-stack sl-stack--vertical sl-stack--8 HttpOperation sl-flex sl-flex-col sl-items-stretch sl-w-full"><div class="sl-stack sl-stack--vertical sl-stack--5 sl-flex sl-flex-col sl-items-stretch"><div class="sl-relative"><div class="sl-stack sl-stack--horizontal sl-stack--5 sl-flex sl-flex-row sl-items-center"><h1 class="sl-text-5xl sl-leading-tight sl-font-prose sl-font-semibold sl-text-heading">User: Create a user</h1><div class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-center"></div></div></div><div class="sl-relative"><div><div title="/{proxy_path}/api/v2/admin/user/" class="sl-stack sl-stack--horizontal sl-stack--3 sl-inline-flex sl-flex-row sl-items-center sl-max-w-full sl-font-mono sl-py-2 sl-pr-4 sl-pl-2.5 sl-bg-canvas-50 sl-rounded-lg"><div class="sl-text-lg sl-font-semibold sl-uppercase sl-px-2.5 sl-py-1 sl-bg-primary sl-text-on-primary sl-rounded-lg">post</div><div class="sl-flex sl-overflow-x-hidden sl-text-lg sl-select-all"><div dir="ltr" class="sl-overflow-x-hidden sl-truncate"><span dir="ltr" class="sl-text-muted" style="unicode-bidi: bidi-override;"></span><span class="sl-flex-1 sl-font-semibold">/{proxy_path}/api/v2/admin/user/</span></div></div></div></div></div></div><div class="sl-flex"><div data-testid="two-column-left" class="sl-flex-1 sl-w-0"><div class="sl-stack sl-stack--vertical sl-stack--10 sl-flex sl-flex-col sl-items-stretch"><div class="sl-stack sl-stack--vertical sl-stack--8 sl-flex sl-flex-col sl-items-stretch"><div class="sl-flex sl-w-full"><h2 id="/paths/proxy_path--api-v2-admin-user/post#Request" aria-label="Request" class="sl-link-heading sl-py-1 sl-pr-6 sl-text-4xl sl-leading-tight sl-font-prose sl-font-bold sl-text-heading"><a href="#/paths/proxy_path--api-v2-admin-user/post#Request" class="sl-link sl-link-heading__link sl-inline-flex sl-items-center sl-text-current"><div>Request</div><div class="sl-link-heading__icon sl-text-base sl-ml-4 sl-text-muted"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="link" class="svg-inline--fa fa-link sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path fill="currentColor" d="M172.5 131.1C228.1 75.51 320.5 75.51 376.1 131.1C426.1 181.1 433.5 260.8 392.4 318.3L391.3 319.9C381 334.2 361 337.6 346.7 327.3C332.3 317 328.9 297 339.2 282.7L340.3 281.1C363.2 249 359.6 205.1 331.7 177.2C300.3 145.8 249.2 145.8 217.7 177.2L105.5 289.5C73.99 320.1 73.99 372 105.5 403.5C133.3 431.4 177.3 435 209.3 412.1L210.9 410.1C225.3 400.7 245.3 404 255.5 418.4C265.8 432.8 262.5 452.8 248.1 463.1L246.5 464.2C188.1 505.3 110.2 498.7 60.21 448.8C3.741 392.3 3.741 300.7 60.21 244.3L172.5 131.1zM467.5 380C411 436.5 319.5 436.5 263 380C213 330 206.5 251.2 247.6 193.7L248.7 192.1C258.1 177.8 278.1 174.4 293.3 184.7C307.7 194.1 311.1 214.1 300.8 229.3L299.7 230.9C276.8 262.1 280.4 306.9 308.3 334.8C339.7 366.2 390.8 366.2 422.3 334.8L534.5 222.5C566 191 566 139.1 534.5 108.5C506.7 80.63 462.7 76.99 430.7 99.9L429.1 101C414.7 111.3 394.7 107.1 384.5 93.58C374.2 79.2 377.5 59.21 391.9 48.94L393.5 47.82C451 6.731 529.8 13.25 579.8 63.24C636.3 119.7 636.3 211.3 579.8 267.7L467.5 380z"></path></svg></div></a></h2><div class="sl-flex sl-flex-grow sl-self-center sl-py-1" style="min-width: 0px;"></div></div><div class="sl-stack sl-stack--vertical sl-stack--3 sl-flex sl-flex-col sl-items-stretch"><div data-test="http-operation-security-row" class="sl-relative sl-p-0"><div class="sl-panel sl-outline-none sl-w-full sl-rounded-lg sl-border"><div aria-expanded="true" tabindex="0" class="sl-panel__titlebar sl-flex sl-items-center sl-relative focus:sl-z-10 sl-text-base sl-leading-none sl-font-medium sl-pr-4 sl-pl-3 sl-border-input focus:sl-border-primary sl-cursor-pointer sl-select-none" role="button"><div class="sl-flex sl-flex-1 sl-items-center sl-h-lg"><div class="sl-flex sl-items-center sl-mr-1.5"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" class="svg-inline--fa fa-chevron-down fa-fw fa-sm sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M224 416c-8.188 0-16.38-3.125-22.62-9.375l-192-192c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L224 338.8l169.4-169.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-192 192C240.4 412.9 232.2 416 224 416z"></path></svg></div><div role="heading">Security: API Key</div></div></div><div class="sl-panel__content-wrapper sl-border-t" role="region"><div class="sl-panel__content sl-p-4"><div class="sl--m-2"><div><div class="sl-m-2 sl-p-2 sl-border"><div class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>An API key is a token that you provide when making API calls. Include the token in a header parameter called <code class="sl-font-mono sl-font-medium sl-mx-0.5 sl-px-1 sl-py-0.5 sl-bg-code sl-text-on-code sl-rounded sl-border" style="font-size: 0.8125em;">Hiddify-API-Key</code>.</p>
<p>Example: <code class="sl-font-mono sl-font-medium sl-mx-0.5 sl-px-1 sl-py-0.5 sl-bg-code sl-text-on-code sl-rounded sl-border" style="font-size: 0.8125em;">Hiddify-API-Key: 123</code></p></div></div></div></div></div></div></div></div></div><div class="sl-stack sl-stack--vertical sl-stack--5 sl-flex sl-flex-col sl-items-stretch"><div class="sl-flex sl-w-full"><h3 id="/paths/proxy_path--api-v2-admin-user/post#Path-Parameters" aria-label="Path Parameters" class="sl-link-heading sl-py-1 sl-pr-6 sl-text-2xl sl-leading-snug sl-font-prose sl-font-semibold sl-text-heading"><a href="#/paths/proxy_path--api-v2-admin-user/post#Path-Parameters" class="sl-link sl-link-heading__link sl-inline-flex sl-items-center sl-text-current"><div>Path Parameters</div><div class="sl-link-heading__icon sl-text-base sl-ml-4 sl-text-muted"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="link" class="svg-inline--fa fa-link sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path fill="currentColor" d="M172.5 131.1C228.1 75.51 320.5 75.51 376.1 131.1C426.1 181.1 433.5 260.8 392.4 318.3L391.3 319.9C381 334.2 361 337.6 346.7 327.3C332.3 317 328.9 297 339.2 282.7L340.3 281.1C363.2 249 359.6 205.1 331.7 177.2C300.3 145.8 249.2 145.8 217.7 177.2L105.5 289.5C73.99 320.1 73.99 372 105.5 403.5C133.3 431.4 177.3 435 209.3 412.1L210.9 410.1C225.3 400.7 245.3 404 255.5 418.4C265.8 432.8 262.5 452.8 248.1 463.1L246.5 464.2C188.1 505.3 110.2 498.7 60.21 448.8C3.741 392.3 3.741 300.7 60.21 244.3L172.5 131.1zM467.5 380C411 436.5 319.5 436.5 263 380C213 330 206.5 251.2 247.6 193.7L248.7 192.1C258.1 177.8 278.1 174.4 293.3 184.7C307.7 194.1 311.1 214.1 300.8 229.3L299.7 230.9C276.8 262.1 280.4 306.9 308.3 334.8C339.7 366.2 390.8 366.2 422.3 334.8L534.5 222.5C566 191 566 139.1 534.5 108.5C506.7 80.63 462.7 76.99 430.7 99.9L429.1 101C414.7 111.3 394.7 107.1 384.5 93.58C374.2 79.2 377.5 59.21 391.9 48.94L393.5 47.82C451 6.731 529.8 13.25 579.8 63.24C636.3 119.7 636.3 211.3 579.8 267.7L467.5 380z"></path></svg></div></a></h3><div class="sl-flex sl-flex-grow sl-self-center sl-py-1" style="min-width: 0px;"></div></div><div class="" id="mosaic-provider-react-aria-1-1"><div data-overlay-container="true" class=""><div class="JsonSchemaViewer"><div></div><div data-level="0" class="sl-text-sm"><div data-id="bee722eef7b5c" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2"><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-proxy_path" class="sl-font-mono sl-font-semibold sl-mr-2">proxy_path</div><span data-test="property-type" class="sl-truncate sl-text-muted">string</span></div><div class="sl-flex-1 sl-h-px sl-mx-3"></div><span data-test="property-required" class="sl-ml-2 sl-text-warning">required</span></div></div></div></div></div></div></div></div><div class="sl-stack sl-stack--vertical sl-stack--6 sl-flex sl-flex-col sl-items-stretch"><div class="sl-flex sl-w-full"><h3 id="/paths/proxy_path--api-v2-admin-user/post#request-body" aria-label="Body" class="sl-link-heading sl-py-1 sl-pr-6 sl-text-2xl sl-leading-snug sl-font-prose sl-font-semibold sl-text-heading"><a href="#/paths/proxy_path--api-v2-admin-user/post#request-body" class="sl-link sl-link-heading__link sl-inline-flex sl-items-center sl-text-current"><div>Body</div><div class="sl-link-heading__icon sl-text-base sl-ml-4 sl-text-muted"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="link" class="svg-inline--fa fa-link sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path fill="currentColor" d="M172.5 131.1C228.1 75.51 320.5 75.51 376.1 131.1C426.1 181.1 433.5 260.8 392.4 318.3L391.3 319.9C381 334.2 361 337.6 346.7 327.3C332.3 317 328.9 297 339.2 282.7L340.3 281.1C363.2 249 359.6 205.1 331.7 177.2C300.3 145.8 249.2 145.8 217.7 177.2L105.5 289.5C73.99 320.1 73.99 372 105.5 403.5C133.3 431.4 177.3 435 209.3 412.1L210.9 410.1C225.3 400.7 245.3 404 255.5 418.4C265.8 432.8 262.5 452.8 248.1 463.1L246.5 464.2C188.1 505.3 110.2 498.7 60.21 448.8C3.741 392.3 3.741 300.7 60.21 244.3L172.5 131.1zM467.5 380C411 436.5 319.5 436.5 263 380C213 330 206.5 251.2 247.6 193.7L248.7 192.1C258.1 177.8 278.1 174.4 293.3 184.7C307.7 194.1 311.1 214.1 300.8 229.3L299.7 230.9C276.8 262.1 280.4 306.9 308.3 334.8C339.7 366.2 390.8 366.2 422.3 334.8L534.5 222.5C566 191 566 139.1 534.5 108.5C506.7 80.63 462.7 76.99 430.7 99.9L429.1 101C414.7 111.3 394.7 107.1 384.5 93.58C374.2 79.2 377.5 59.21 391.9 48.94L393.5 47.82C451 6.731 529.8 13.25 579.8 63.24C636.3 119.7 636.3 211.3 579.8 267.7L467.5 380z"></path></svg></div></a></h3><div class="sl-flex sl-flex-grow sl-self-center sl-py-1" style="min-width: 0px;"><div class="sl-flex sl-flex-1 sl-justify-end"><div class="sl-select sl-relative"><div aria-hidden="true" style="border: 0px; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(50%); height: 1px; margin: 0px -1px -1px 0px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"><input type="text" tabindex="0" style="font-size: 16px;"><label><select size="1" tabindex="-1"><option></option><option value="0">application/json</option></select></label></div><div class="sl-relative"><button aria-label="Request Body Content Type" aria-haspopup="listbox" aria-expanded="false" id="react-aria-1-6" aria-labelledby="react-aria-1-6 react-aria-1-8" type="button" class="sl-button sl-form-group-border sl-w-full sl-h-sm sl-text-base sl-font-normal sl-px-1.5 sl-bg-transparent sl-rounded sl-border-transparent hover:sl-border-input focus:sl-border-primary active:sl-border-primary sl-border disabled:sl-opacity-60"><div class="sl-flex sl-flex-1 sl-justify-items-start sl-items-center"><div class="sl-pr-1">application/json</div></div><div class="sl-text-xs sl--mr-0.5 sl-ml-1"><div class="sl-pt-0.5 sl-pr-0.5"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" class="svg-inline--fa fa-chevron-down fa-xs sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M224 416c-8.188 0-16.38-3.125-22.62-9.375l-192-192c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L224 338.8l169.4-169.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-192 192C240.4 412.9 232.2 416 224 416z"></path></svg></div></div></button></div></div></div></div></div><div class="" id="mosaic-provider-react-aria-2-1"><div data-overlay-container="true" class=""><div class="JsonSchemaViewer"><div></div><div data-level="0" class="sl-text-sm sl-ml-px sl-border-l"><div data-id="6e881d6f555bd" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-added_by_uuid" class="sl-font-mono sl-font-semibold sl-mr-2">added_by_uuid</div><span data-test="property-type" class="sl-truncate sl-text-muted">any</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>UUID of the admin who added this user</p></div></div></div><div data-id="714ad0b890604" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-comment" class="sl-font-mono sl-font-semibold sl-mr-2">comment</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>An optional comment about the user</p></div><div data-test="property-validation" class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-baseline sl-text-muted"><span>Default:</span><div class="sl-flex sl-flex-1 sl-flex-wrap" style="gap: 4px;"><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">null</span></div></div></div></div><div data-id="846252fe54588" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-current_usage_GB" class="sl-font-mono sl-font-semibold sl-mr-2">current_usage_GB</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">number</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The current data usage of the user in gigabytes</p></div></div></div><div data-id="b6a74646eda6b" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-ed25519_private_key" class="sl-font-mono sl-font-semibold sl-mr-2">ed25519_private_key</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically, The user's private key using the Ed25519 algorithm</p></div></div></div><div data-id="4c1757561c29a" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-ed25519_public_key" class="sl-font-mono sl-font-semibold sl-mr-2">ed25519_public_key</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically,The user's public key using the Ed25519 algorithm</p></div></div></div><div data-id="73648143942af" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-enable" class="sl-font-mono sl-font-semibold sl-mr-2">enable</div><span data-test="property-type" class="sl-truncate sl-text-muted">boolean</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>Whether the user is enabled or not</p></div></div></div><div data-id="f807af3112d71" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-is_active" class="sl-font-mono sl-font-semibold sl-mr-2">is_active</div><span data-test="property-type" class="sl-truncate sl-text-muted">boolean</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>Whether the user is active for using hiddify</p></div></div></div><div data-id="25ddb2864ab7c" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-lang" class="sl-font-mono sl-font-semibold sl-mr-2">lang</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The language of the user</p></div><div data-test="property-validation" class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-baseline sl-text-muted"><span>Allowed values:</span><div class="sl-flex sl-flex-1 sl-flex-wrap" style="gap: 4px;"><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">en</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">fa</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">ru</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">pt</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">zh</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">null</span></div></div></div></div><div data-id="2095eacfcd14a" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-last_online" class="sl-font-mono sl-font-semibold sl-mr-2">last_online</div><span data-test="property-type" class="sl-truncate sl-text-muted">&lt;%Y-%m-%d %H:%M:%S&gt;</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The last time the user was online, converted to a JSON-friendly format</p></div></div></div><div data-id="7ce4dcd5e4c4d" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-last_reset_time" class="sl-font-mono sl-font-semibold sl-mr-2">last_reset_time</div><span data-test="property-type" class="sl-truncate sl-text-muted">&lt;%Y-%m-%d %H:%M:%S&gt;</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The last time the user's data usage was reset, in a JSON-friendly format</p></div></div></div><div data-id="e51d5207cbb77" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-mode" class="sl-font-mono sl-font-semibold sl-mr-2">mode</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The mode of the user's account, which dictates access level or type</p></div><div data-test="property-validation" class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-baseline sl-text-muted"><span>Allowed values:</span><div class="sl-flex sl-flex-1 sl-flex-wrap" style="gap: 4px;"><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">no_reset</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">monthly</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">weekly</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">daily</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">null</span></div></div></div></div><div data-id="53bed0ec0ff23" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-name" class="sl-font-mono sl-font-semibold sl-mr-2">name</div><span data-test="property-type" class="sl-truncate sl-text-muted">string</span></div><div class="sl-flex-1 sl-h-px sl-mx-3"></div><span data-test="property-required" class="sl-ml-2 sl-text-warning">required</span></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>Name of the user</p></div></div></div><div data-id="c40e8ed1e8467" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-package_days" class="sl-font-mono sl-font-semibold sl-mr-2">package_days</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">integer</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The number of days in the user's package</p></div></div></div><div data-id="e9d36d73db215" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-start_date" class="sl-font-mono sl-font-semibold sl-mr-2">start_date</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The start date of the user's package, in a JSON-friendly format</p></div><div data-test="property-validation" class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-baseline sl-text-muted"><span>Match pattern:</span><div class="sl-flex sl-flex-1 sl-flex-wrap" style="gap: 4px;"><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">null</span></div></div></div></div><div data-id="38c69441a74e0" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-telegram_id" class="sl-font-mono sl-font-semibold sl-mr-2">telegram_id</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">integer</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The Telegram ID associated with the user</p></div></div></div><div data-id="4797222201d1d" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-usage_limit_GB" class="sl-font-mono sl-font-semibold sl-mr-2">usage_limit_GB</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">number</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The data usage limit for the user in gigabytes</p></div></div></div><div data-id="a3692da0e986e" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-uuid" class="sl-font-mono sl-font-semibold sl-mr-2">uuid</div><span data-test="property-type" class="sl-truncate sl-text-muted">any</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>Unique identifier for the user</p></div></div></div><div data-id="a1a9fe1b3c8c3" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-wg_pk" class="sl-font-mono sl-font-semibold sl-mr-2">wg_pk</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically, The user's WireGuard private key</p></div></div></div><div data-id="59fac84619d25" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-wg_psk" class="sl-font-mono sl-font-semibold sl-mr-2">wg_psk</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically, The user's WireGuard preshared key</p></div></div></div><div data-id="ba0ac84660bc4" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-wg_pub" class="sl-font-mono sl-font-semibold sl-mr-2">wg_pub</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically, The user's WireGuard public key</p></div></div></div></div></div></div></div></div></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-col sl-w-full sl-h-auto"><div class="sl-flex sl-w-full"><h2 id="/paths/proxy_path--api-v2-admin-user/post#Responses" aria-label="Responses" class="sl-link-heading sl-py-1 sl-pr-6 sl-text-4xl sl-leading-tight sl-font-prose sl-font-bold sl-text-heading"><a href="#/paths/proxy_path--api-v2-admin-user/post#Responses" class="sl-link sl-link-heading__link sl-inline-flex sl-items-center sl-text-current"><div>Responses</div><div class="sl-link-heading__icon sl-text-base sl-ml-4 sl-text-muted"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="link" class="svg-inline--fa fa-link sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path fill="currentColor" d="M172.5 131.1C228.1 75.51 320.5 75.51 376.1 131.1C426.1 181.1 433.5 260.8 392.4 318.3L391.3 319.9C381 334.2 361 337.6 346.7 327.3C332.3 317 328.9 297 339.2 282.7L340.3 281.1C363.2 249 359.6 205.1 331.7 177.2C300.3 145.8 249.2 145.8 217.7 177.2L105.5 289.5C73.99 320.1 73.99 372 105.5 403.5C133.3 431.4 177.3 435 209.3 412.1L210.9 410.1C225.3 400.7 245.3 404 255.5 418.4C265.8 432.8 262.5 452.8 248.1 463.1L246.5 464.2C188.1 505.3 110.2 498.7 60.21 448.8C3.741 392.3 3.741 300.7 60.21 244.3L172.5 131.1zM467.5 380C411 436.5 319.5 436.5 263 380C213 330 206.5 251.2 247.6 193.7L248.7 192.1C258.1 177.8 278.1 174.4 293.3 184.7C307.7 194.1 311.1 214.1 300.8 229.3L299.7 230.9C276.8 262.1 280.4 306.9 308.3 334.8C339.7 366.2 390.8 366.2 422.3 334.8L534.5 222.5C566 191 566 139.1 534.5 108.5C506.7 80.63 462.7 76.99 430.7 99.9L429.1 101C414.7 111.3 394.7 107.1 384.5 93.58C374.2 79.2 377.5 59.21 391.9 48.94L393.5 47.82C451 6.731 529.8 13.25 579.8 63.24C636.3 119.7 636.3 211.3 579.8 267.7L467.5 380z"></path></svg></div></a></h2><div class="sl-flex sl-flex-grow sl-self-center sl-py-1" style="min-width: 0px;"><div id="react-aria-2-13" aria-orientation="horizontal" class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-overflow-x-auto sl-overflow-y-hidden sl-flex-row sl-h-auto sl-text-lg" role="tablist"><div tabindex="0" data-key="200" id="react-aria-2-13-tab-200" aria-selected="true" aria-controls="react-aria-2-13-tabpanel-200" class="sl-font-medium sl-p-1 sl-px-2 sl-py-1 sl-bg-success sl-text-on-primary sl-rounded-lg sl-border-light sl-cursor" role="tab">200</div><div tabindex="-1" data-key="401" id="react-aria-2-13-tab-401" aria-selected="false" class="sl-font-medium sl-p-1 sl-px-2 sl-py-1 hover:sl-bg-warning-tint sl-text-warning hover:sl-text-body sl-rounded-lg sl-border-light sl-cursor-pointer" role="tab">401</div><div tabindex="-1" data-key="404" id="react-aria-2-13-tab-404" aria-selected="false" class="sl-font-medium sl-p-1 sl-px-2 sl-py-1 hover:sl-bg-warning-tint sl-text-warning hover:sl-text-body sl-rounded-lg sl-border-light sl-cursor-pointer" role="tab">404</div><div tabindex="-1" data-key="422" id="react-aria-2-13-tab-422" aria-selected="false" class="sl-font-medium sl-p-1 sl-px-2 sl-py-1 hover:sl-bg-warning-tint sl-text-warning hover:sl-text-body sl-rounded-lg sl-border-light sl-cursor-pointer" role="tab">422</div></div></div></div><div id="undefined-tabpanel-undefined" aria-labelledby="react-aria-2-13-tab-200" class="sl-flex-1 sl-py-0" role="tabpanel"><div class="sl-stack sl-stack--vertical sl-stack--8 sl-flex sl-flex-col sl-items-stretch sl-pt-8"><div class="sl-relative"><div class="sl-prose sl-markdown-viewer "><p>Successful response</p></div></div><div class="sl-flex sl-w-full"><h3 id="/paths/proxy_path--api-v2-admin-user/post#response-body" aria-label="Body" class="sl-link-heading sl-py-1 sl-pr-6 sl-text-2xl sl-leading-snug sl-font-prose sl-font-semibold sl-text-heading"><a href="#/paths/proxy_path--api-v2-admin-user/post#response-body" class="sl-link sl-link-heading__link sl-inline-flex sl-items-center sl-text-current"><div>Body</div><div class="sl-link-heading__icon sl-text-base sl-ml-4 sl-text-muted"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="link" class="svg-inline--fa fa-link sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path fill="currentColor" d="M172.5 131.1C228.1 75.51 320.5 75.51 376.1 131.1C426.1 181.1 433.5 260.8 392.4 318.3L391.3 319.9C381 334.2 361 337.6 346.7 327.3C332.3 317 328.9 297 339.2 282.7L340.3 281.1C363.2 249 359.6 205.1 331.7 177.2C300.3 145.8 249.2 145.8 217.7 177.2L105.5 289.5C73.99 320.1 73.99 372 105.5 403.5C133.3 431.4 177.3 435 209.3 412.1L210.9 410.1C225.3 400.7 245.3 404 255.5 418.4C265.8 432.8 262.5 452.8 248.1 463.1L246.5 464.2C188.1 505.3 110.2 498.7 60.21 448.8C3.741 392.3 3.741 300.7 60.21 244.3L172.5 131.1zM467.5 380C411 436.5 319.5 436.5 263 380C213 330 206.5 251.2 247.6 193.7L248.7 192.1C258.1 177.8 278.1 174.4 293.3 184.7C307.7 194.1 311.1 214.1 300.8 229.3L299.7 230.9C276.8 262.1 280.4 306.9 308.3 334.8C339.7 366.2 390.8 366.2 422.3 334.8L534.5 222.5C566 191 566 139.1 534.5 108.5C506.7 80.63 462.7 76.99 430.7 99.9L429.1 101C414.7 111.3 394.7 107.1 384.5 93.58C374.2 79.2 377.5 59.21 391.9 48.94L393.5 47.82C451 6.731 529.8 13.25 579.8 63.24C636.3 119.7 636.3 211.3 579.8 267.7L467.5 380z"></path></svg></div></a></h3><div class="sl-flex sl-flex-grow sl-self-center sl-py-1" style="min-width: 0px;"><div class="sl-flex sl-flex-1 sl-justify-end"><div class="sl-select sl-relative"><div aria-hidden="true" style="border: 0px; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(50%); height: 1px; margin: 0px -1px -1px 0px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"><input type="text" tabindex="0" style="font-size: 16px;"><label><select size="1" tabindex="-1"><option></option><option value="0">application/json</option></select></label></div><div class="sl-relative"><button aria-label="Response Body Content Type" aria-haspopup="listbox" aria-expanded="false" id="react-aria-2-17" aria-labelledby="react-aria-2-17 react-aria-2-19" type="button" class="sl-button sl-form-group-border sl-w-full sl-h-sm sl-text-base sl-font-normal sl-px-1.5 sl-bg-transparent sl-rounded sl-border-transparent hover:sl-border-input focus:sl-border-primary active:sl-border-primary sl-border disabled:sl-opacity-60"><div class="sl-flex sl-flex-1 sl-justify-items-start sl-items-center"><div class="sl-pr-1">application/json</div></div><div class="sl-text-xs sl--mr-0.5 sl-ml-1"><div class="sl-pt-0.5 sl-pr-0.5"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" class="svg-inline--fa fa-chevron-down fa-xs sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M224 416c-8.188 0-16.38-3.125-22.62-9.375l-192-192c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L224 338.8l169.4-169.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-192 192C240.4 412.9 232.2 416 224 416z"></path></svg></div></div></button></div></div></div></div></div><div class="" id="mosaic-provider-react-aria-3-1"><div data-overlay-container="true" class=""><div class="JsonSchemaViewer"><div class="sl-stack sl-stack--horizontal sl-stack--1 sl-flex sl-flex-row sl-items-center sl-sticky sl-top-0 sl-z-10 sl-h-md sl-text-sm sl-leading-none sl-font-mono sl--mt-8 sl-px-px sl-bg-canvas-pure sl-text-light sl-border-b"><div>responses</div><div>/</div><div>200</div></div><div></div><div data-level="0" class="sl-text-sm sl-ml-px sl-border-l"><div data-id="0bbc5107828a8" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-added_by_uuid" class="sl-font-mono sl-font-semibold sl-mr-2">added_by_uuid</div><span data-test="property-type" class="sl-truncate sl-text-muted">any</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>UUID of the admin who added this user</p></div></div></div><div data-id="42f4c3d3ad7fe" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-comment" class="sl-font-mono sl-font-semibold sl-mr-2">comment</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>An optional comment about the user</p></div><div data-test="property-validation" class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-baseline sl-text-muted"><span>Default:</span><div class="sl-flex sl-flex-1 sl-flex-wrap" style="gap: 4px;"><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">null</span></div></div></div></div><div data-id="ff1df7614cc9b" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-current_usage_GB" class="sl-font-mono sl-font-semibold sl-mr-2">current_usage_GB</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">number</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The current data usage of the user in gigabytes</p></div></div></div><div data-id="6b43f741fcb34" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-ed25519_private_key" class="sl-font-mono sl-font-semibold sl-mr-2">ed25519_private_key</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically, The user's private key using the Ed25519 algorithm</p></div></div></div><div data-id="b541a958bf115" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-ed25519_public_key" class="sl-font-mono sl-font-semibold sl-mr-2">ed25519_public_key</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically,The user's public key using the Ed25519 algorithm</p></div></div></div><div data-id="ac1544edfee8f" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-enable" class="sl-font-mono sl-font-semibold sl-mr-2">enable</div><span data-test="property-type" class="sl-truncate sl-text-muted">boolean</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>Whether the user is enabled or not</p></div></div></div><div data-id="1b83f88b29fdd" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-id" class="sl-font-mono sl-font-semibold sl-mr-2">id</div><span data-test="property-type" class="sl-truncate sl-text-muted">integer</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>never use it, only for better presentation</p></div></div></div><div data-id="8ba8a2735337a" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-is_active" class="sl-font-mono sl-font-semibold sl-mr-2">is_active</div><span data-test="property-type" class="sl-truncate sl-text-muted">boolean</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>Whether the user is active for using hiddify</p></div></div></div><div data-id="ba7e036734f56" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-lang" class="sl-font-mono sl-font-semibold sl-mr-2">lang</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The language of the user</p></div><div data-test="property-validation" class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-baseline sl-text-muted"><span>Allowed values:</span><div class="sl-flex sl-flex-1 sl-flex-wrap" style="gap: 4px;"><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">en</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">fa</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">ru</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">pt</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">zh</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">null</span></div></div></div></div><div data-id="3464aee8d09cb" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-last_online" class="sl-font-mono sl-font-semibold sl-mr-2">last_online</div><span data-test="property-type" class="sl-truncate sl-text-muted">&lt;%Y-%m-%d %H:%M:%S&gt;</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The last time the user was online, converted to a JSON-friendly format</p></div></div></div><div data-id="a5a54572863e4" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-last_reset_time" class="sl-font-mono sl-font-semibold sl-mr-2">last_reset_time</div><span data-test="property-type" class="sl-truncate sl-text-muted">&lt;%Y-%m-%d %H:%M:%S&gt;</span></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The last time the user's data usage was reset, in a JSON-friendly format</p></div></div></div><div data-id="8e3e71795d6d6" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-mode" class="sl-font-mono sl-font-semibold sl-mr-2">mode</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The mode of the user's account, which dictates access level or type</p></div><div data-test="property-validation" class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-baseline sl-text-muted"><span>Allowed values:</span><div class="sl-flex sl-flex-1 sl-flex-wrap" style="gap: 4px;"><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">no_reset</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">monthly</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">weekly</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">daily</span><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">null</span></div></div></div></div><div data-id="fcdce4d90aadd" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-name" class="sl-font-mono sl-font-semibold sl-mr-2">name</div><span data-test="property-type" class="sl-truncate sl-text-muted">string</span></div><div class="sl-flex-1 sl-h-px sl-mx-3"></div><span data-test="property-required" class="sl-ml-2 sl-text-warning">required</span></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>Name of the user</p></div></div></div><div data-id="4d46a42bb2495" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-package_days" class="sl-font-mono sl-font-semibold sl-mr-2">package_days</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">integer</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The number of days in the user's package</p></div></div></div><div data-id="56ed305d8b917" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-start_date" class="sl-font-mono sl-font-semibold sl-mr-2">start_date</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The start date of the user's package, in a JSON-friendly format</p></div><div data-test="property-validation" class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-baseline sl-text-muted"><span>Match pattern:</span><div class="sl-flex sl-flex-1 sl-flex-wrap" style="gap: 4px;"><span class="sl-max-w-full sl-break-all sl-px-1 sl-bg-canvas-tint sl-text-muted sl-rounded sl-border">null</span></div></div></div></div><div data-id="8b24e6c66e2ba" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-telegram_id" class="sl-font-mono sl-font-semibold sl-mr-2">telegram_id</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">integer</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The Telegram ID associated with the user</p></div></div></div><div data-id="3f3cef98a2266" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-usage_limit_GB" class="sl-font-mono sl-font-semibold sl-mr-2">usage_limit_GB</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">number</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>The data usage limit for the user in gigabytes</p></div></div></div><div data-id="eab2d45a002c4" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-uuid" class="sl-font-mono sl-font-semibold sl-mr-2">uuid</div><span data-test="property-type" class="sl-truncate sl-text-muted">any</span></div><div class="sl-flex-1 sl-h-px sl-mx-3"></div><span data-test="property-required" class="sl-ml-2 sl-text-warning">required</span></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>Unique identifier for the user</p></div></div></div><div data-id="159f5252b9f1e" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-wg_pk" class="sl-font-mono sl-font-semibold sl-mr-2">wg_pk</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically, The user's WireGuard private key</p></div></div></div><div data-id="2e28e29544df4" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-wg_psk" class="sl-font-mono sl-font-semibold sl-mr-2">wg_psk</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically, The user's WireGuard preshared key</p></div></div></div><div data-id="bc98e294df31c" data-test="schema-row" class="sl-flex sl-relative sl-max-w-full sl-py-2 sl-pl-3"><div class="sl-w-3 sl-mt-2 sl-mr-3 sl--ml-3 sl-border-t"></div><div class="sl-stack sl-stack--vertical sl-stack--1 sl-flex sl-flex-1 sl-flex-col sl-items-stretch sl-max-w-full"><div class="sl-flex sl-items-center sl-max-w-full"><div class="sl-flex sl-items-baseline sl-text-base"><div data-test="property-name-wg_pub" class="sl-font-mono sl-font-semibold sl-mr-2">wg_pub</div><div class="sl-truncate"><span data-test="property-type" class="sl-truncate sl-text-muted">string</span><span class="sl-text-muted"> or </span><span data-test="property-type" class="sl-truncate sl-text-muted">null</span></div></div></div><div data-test="property-description" class="sl-prose sl-markdown-viewer " style="font-size: 12px;"><p>If empty, it will be created automatically, The user's WireGuard public key</p></div></div></div></div></div></div></div></div></div></div></div></div><div data-testid="two-column-right" class="sl-relative sl-w-2/5 sl-ml-16" style="max-width: 500px;"><div class="sl-stack sl-stack--vertical sl-stack--6 sl-flex sl-flex-col sl-items-stretch"><div class="sl-inverted"><div class="sl-overflow-y-hidden sl-rounded-lg"><div class="TryItPanel sl-bg-canvas-100"><div data-test="try-it-auth" class="sl-panel sl-outline-none sl-w-full"><div aria-expanded="true" tabindex="0" class="sl-panel__titlebar sl-flex sl-items-center sl-relative focus:sl-z-10 sl-text-base sl-leading-none sl-pr-4 sl-pl-3 sl-bg-canvas-200 sl-text-body sl-border-input focus:sl-border-primary sl-cursor-pointer sl-select-none" role="button"><div class="sl-flex sl-flex-1 sl-items-center sl-h-lg"><div class="sl-flex sl-items-center sl-mr-1.5"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="caret-down" class="svg-inline--fa fa-caret-down fa-fw sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M310.6 246.6l-127.1 128C176.4 380.9 168.2 384 160 384s-16.38-3.125-22.63-9.375l-127.1-128C.2244 237.5-2.516 223.7 2.438 211.8S19.07 192 32 192h255.1c12.94 0 24.62 7.781 29.58 19.75S319.8 237.5 310.6 246.6z"></path></svg></div>Auth</div></div><div class="sl-panel__content-wrapper sl-bg-canvas-100" role="region"><div data-test="auth-try-it-row" class="ParameterGrid sl-p-4"><label aria-hidden="true" for="id_auth_Hiddify-API-Key_6Se2mKnF">Hiddify-API-Key</label><span class="sl-mx-3">:</span><div class="sl-flex sl-flex-1"><div class="sl-input sl-form-group-border sl-flex-1 sl-relative"><input id="id_auth_Hiddify-API-Key_6Se2mKnF" aria-label="Hiddify-API-Key" placeholder="123" type="text" aria-required="true" class="sl-form-group-border sl-relative sl-w-full sl-h-md sl-text-base sl-pr-2.5 sl-pl-2.5 sl-rounded sl-border-transparent hover:sl-border-input focus:sl-border-primary sl-border" value=""></div></div></div></div></div><div class="sl-panel sl-outline-none sl-w-full"><div aria-expanded="true" tabindex="0" class="sl-panel__titlebar sl-flex sl-items-center sl-relative focus:sl-z-10 sl-text-base sl-leading-none sl-pr-4 sl-pl-3 sl-bg-canvas-200 sl-text-body sl-border-input focus:sl-border-primary sl-cursor-pointer sl-select-none" role="button"><div class="sl-flex sl-flex-1 sl-items-center sl-h-lg"><div class="sl-flex sl-items-center sl-mr-1.5"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="caret-down" class="svg-inline--fa fa-caret-down fa-fw sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M310.6 246.6l-127.1 128C176.4 380.9 168.2 384 160 384s-16.38-3.125-22.63-9.375l-127.1-128C.2244 237.5-2.516 223.7 2.438 211.8S19.07 192 32 192h255.1c12.94 0 24.62 7.781 29.58 19.75S319.8 237.5 310.6 246.6z"></path></svg></div>Parameters</div></div><div class="sl-panel__content-wrapper sl-bg-canvas-100" role="region"><div class="sl-overflow-y-auto ParameterGrid OperationParametersContent sl-p-4"><label aria-hidden="true" data-testid="param-label" for="id_proxy_path_Gtgk1_9s" class="sl-text-base">proxy_path*</label><span class="sl-mx-3">:</span><div><div class="sl-flex sl-flex-1"><div class="sl-input sl-form-group-border sl-flex-1 sl-relative"><input id="id_proxy_path_Gtgk1_9s" aria-label="proxy_path" placeholder="string" type="text" aria-required="true" class="sl-form-group-border sl-relative sl-w-full sl-h-md sl-text-base sl-pr-2.5 sl-pl-2.5 sl-rounded sl-border-transparent hover:sl-border-input focus:sl-border-primary sl-border" value=""></div></div></div></div></div></div><div class="sl-pb-1"><div class="sl-panel sl-outline-none sl-w-full"><div aria-expanded="true" tabindex="0" class="sl-panel__titlebar sl-flex sl-items-center sl-relative focus:sl-z-10 sl-text-base sl-leading-none sl-pr-4 sl-pl-3 sl-bg-canvas-200 sl-text-body sl-border-input focus:sl-border-primary sl-cursor-pointer sl-select-none" role="button"><div class="sl-flex sl-flex-1 sl-items-center sl-h-lg"><div class="sl-flex sl-items-center sl-mr-1.5"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="caret-down" class="svg-inline--fa fa-caret-down fa-fw sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M310.6 246.6l-127.1 128C176.4 380.9 168.2 384 160 384s-16.38-3.125-22.63-9.375l-127.1-128C.2244 237.5-2.516 223.7 2.438 211.8S19.07 192 32 192h255.1c12.94 0 24.62 7.781 29.58 19.75S319.8 237.5 310.6 246.6z"></path></svg></div>Body</div></div><div class="sl-panel__content-wrapper sl-bg-canvas-100" role="region"><div class="TextRequestBody sl-p-4"><div id="react-aria-2-12" class="sl-code-editor" style="font-family: var(--font-code); font-size: 12px; line-height: var(--lh-code);"><div style="position: relative; text-align: left; box-sizing: border-box; padding: 0px; overflow: hidden; font-size: 12px;"><textarea class="npm__react-simple-code-editor__textarea sl-border-transparent hover:sl-border-input focus:sl-border-primary" autocapitalize="off" autocomplete="off" autocorrect="off" spellcheck="false" data-gramm="false" style="margin: 0px; background: none; box-sizing: inherit; display: inherit; font-family: inherit; font-size: inherit; font-style: inherit; font-variant-ligatures: inherit; font-weight: inherit; letter-spacing: inherit; line-height: inherit; tab-size: inherit; text-indent: inherit; text-rendering: inherit; text-transform: inherit; white-space: pre-wrap; word-break: keep-all; overflow-wrap: break-word; position: absolute; top: 0px; left: 0px; height: 100%; width: 100%; resize: none; color: inherit; overflow: hidden; -webkit-font-smoothing: antialiased; -webkit-text-fill-color: transparent; padding: 0px;">{
  "added_by_uuid": null,
  "comment": null,
  "current_usage_GB": 0,
  "ed25519_private_key": "string",
  "ed25519_public_key": "string",
  "enable": true,
  "is_active": true,
  "lang": "en",
  "last_online": null,
  "last_reset_time": null,
  "mode": "no_reset",
  "name": "string",
  "package_days": 0,
  "start_date": "string",
  "telegram_id": 0,
  "usage_limit_GB": 0,
  "uuid": null,
  "wg_pk": "string",
  "wg_psk": "string",
  "wg_pub": "string"
}</textarea><pre aria-hidden="true" style="margin: 0px; background: none; box-sizing: inherit; display: inherit; font-family: inherit; font-size: inherit; font-style: inherit; font-variant-ligatures: inherit; font-weight: inherit; letter-spacing: inherit; line-height: inherit; tab-size: inherit; text-indent: inherit; text-rendering: inherit; text-transform: inherit; white-space: pre-wrap; word-break: keep-all; overflow-wrap: break-word; position: relative; pointer-events: none; padding: 0px; color: var(--color-text);"><div class="sl-code-highlight prism-code language-json" style="font-size: 12px;"><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">1</div><div class="sl-flex-1 sl-break-all"><span class="token punctuation" style="color: inherit;">{</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">2</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"added_by_uuid"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(255, 123, 114);">null</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">3</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"comment"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(255, 123, 114);">null</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">4</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"current_usage_GB"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(247, 140, 108);">0</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">5</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"ed25519_private_key"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(165, 214, 255);">"string"</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">6</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"ed25519_public_key"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(165, 214, 255);">"string"</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">7</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"enable"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token boolean" style="color: rgb(121, 192, 255);">true</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">8</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"is_active"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token boolean" style="color: rgb(121, 192, 255);">true</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">9</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"lang"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(165, 214, 255);">"en"</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">10</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"last_online"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(255, 123, 114);">null</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">11</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"last_reset_time"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(255, 123, 114);">null</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">12</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"mode"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(165, 214, 255);">"no_reset"</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">13</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"name"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(165, 214, 255);">"string"</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">14</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"package_days"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(247, 140, 108);">0</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">15</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"start_date"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(165, 214, 255);">"string"</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">16</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"telegram_id"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(247, 140, 108);">0</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">17</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"usage_limit_GB"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(247, 140, 108);">0</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">18</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"uuid"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(255, 123, 114);">null</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">19</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"wg_pk"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(165, 214, 255);">"string"</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">20</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"wg_psk"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(165, 214, 255);">"string"</span><span class="token punctuation" style="color: inherit;">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">21</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(128, 203, 196);">"wg_pub"</span><span class="token operator" style="color: rgb(255, 123, 114);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(165, 214, 255);">"string"</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">22</div><div class="sl-flex-1 sl-break-all"><span class="token plain"></span><span class="token punctuation" style="color: inherit;">}</span></div></div></div></pre><style type="text/css">
/**
 * Reset the text fill color so that placeholder is visible
 */
.npm__react-simple-code-editor__textarea:empty {
  -webkit-text-fill-color: inherit !important;
}

/**
 * Hack to apply on some CSS on IE10 and IE11
 */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  /**
    * IE doesn't support '-webkit-text-fill-color'
    * So we use 'color: transparent' to make the text transparent on IE
    * Unlike other browsers, it doesn't affect caret color in IE
    */
  .npm__react-simple-code-editor__textarea {
    color: transparent !important;
  }

  .npm__react-simple-code-editor__textarea::selection {
    background-color: #accef7 !important;
    color: transparent !important;
  }
}
</style></div><style type="text/css">.sl-code-editor[id="react-aria-2-12"] textarea {
      padding-left: 28px !important;
      word-break: break-all !important;
    }</style></div></div></div></div></div><div class="SendButtonHolder sl-p-4 sl-pt-0"><div class="sl-stack sl-stack--horizontal sl-stack--2 sl-flex sl-flex-row sl-items-center"><button type="button" class="sl-button sl-form-group-border sl-h-sm sl-text-base sl-font-medium sl-px-1.5 sl-bg-primary hover:sl-bg-primary-dark active:sl-bg-primary-darker disabled:sl-bg-canvas-100 sl-text-on-primary disabled:sl-text-body sl-rounded sl-border-transparent sl-border disabled:sl-opacity-70">Send API Request</button></div></div></div></div></div><div class="sl-panel sl-outline-none sl-w-full sl-rounded-lg"><div class="sl-panel__titlebar sl-flex sl-items-center sl-relative focus:sl-z-10 sl-text-base sl-leading-none sl-pr-3 sl-pl-4 sl-bg-canvas-200 sl-text-body sl-border-input focus:sl-border-primary sl-select-none"><div class="sl-flex sl-flex-1 sl-items-center sl-h-lg"><div class="sl--ml-2"><button type="button" aria-label="Request Sample Language" aria-haspopup="true" aria-expanded="false" id="react-aria-3-21" class="sl-button sl-form-group-border sl-h-sm sl-text-base sl-font-medium sl-px-1.5 hover:sl-bg-canvas-50 active:sl-bg-canvas-100 sl-text-muted hover:sl-text-body focus:sl-text-body sl-rounded sl-border-transparent sl-border disabled:sl-opacity-70">Request Sample: Shell / cURL<div class="sl-text-xs sl--mr-0.5 sl-ml-1"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" class="svg-inline--fa fa-chevron-down fa-fw sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M224 416c-8.188 0-16.38-3.125-22.62-9.375l-192-192c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L224 338.8l169.4-169.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-192 192C240.4 412.9 232.2 416 224 416z"></path></svg></div></button></div></div><button type="button" class="sl-button sl-form-group-border sl-h-sm sl-text-base sl-font-medium sl-px-1.5 hover:sl-bg-canvas-50 active:sl-bg-canvas-100 sl-text-muted hover:sl-text-body focus:sl-text-body sl-rounded sl-border-transparent sl-border disabled:sl-opacity-70"><div class="sl-mx-0"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="copy" class="svg-inline--fa fa-copy fa-fw fa-sm sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M384 96L384 0h-112c-26.51 0-48 21.49-48 48v288c0 26.51 21.49 48 48 48H464c26.51 0 48-21.49 48-48V128h-95.1C398.4 128 384 113.6 384 96zM416 0v96h96L416 0zM192 352V128h-144c-26.51 0-48 21.49-48 48v288c0 26.51 21.49 48 48 48h192c26.51 0 48-21.49 48-48L288 416h-32C220.7 416 192 387.3 192 352z"></path></svg></div></button></div><div class="sl-panel__content-wrapper sl-bg-canvas-100"><div class="sl-panel__content sl-p-0"><pre tabindex="0" aria-label="curl --request POST \
  --url https://ductuspro.ru/{proxy_path}/api/v2/admin/user/ \
  --header 'Accept: application/json' \
  --header 'Content-Type: application/json' \
  --header 'Hiddify-API-Key: 123' \
  --data '{
  &quot;added_by_uuid&quot;: null,
  &quot;comment&quot;: null,
  &quot;current_usage_GB&quot;: 0,
  &quot;ed25519_private_key&quot;: &quot;string&quot;,
  &quot;ed25519_public_key&quot;: &quot;string&quot;,
  &quot;enable&quot;: true,
  &quot;is_active&quot;: true,
  &quot;lang&quot;: &quot;en&quot;,
  &quot;last_online&quot;: null,
  &quot;last_reset_time&quot;: null,
  &quot;mode&quot;: &quot;no_reset&quot;,
  &quot;name&quot;: &quot;string&quot;,
  &quot;package_days&quot;: 0,
  &quot;start_date&quot;: &quot;string&quot;,
  &quot;telegram_id&quot;: 0,
  &quot;usage_limit_GB&quot;: 0,
  &quot;uuid&quot;: null,
  &quot;wg_pk&quot;: &quot;string&quot;,
  &quot;wg_psk&quot;: &quot;string&quot;,
  &quot;wg_pub&quot;: &quot;string&quot;
}'" class="sl-code-viewer sl-grid sl-overflow-x-hidden sl-overflow-y-hidden sl-relative sl-outline-none sl-group" role="group" style="--fs-code: 12;"><div class="sl-code-viewer__scroller sl-overflow-x-auto sl-overflow-y-auto" style="max-height: 400px;"><div class="sl-code-highlight prism-code language-bash" style="padding: 12px 15px; font-family: var(--font-code); font-size: var(--fs-code); line-height: var(--lh-code);"><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token function" style="color: rgb(111, 66, 193);">curl</span><span class="token plain"> --request POST </span><span class="token punctuation" style="color: rgb(51, 51, 51);">\</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token plain">  --url https://ductuspro.ru/</span><span class="token punctuation" style="color: rgb(51, 51, 51);">{</span><span class="token plain">proxy_path</span><span class="token punctuation" style="color: rgb(51, 51, 51);">}</span><span class="token plain">/api/v2/admin/user/ </span><span class="token punctuation" style="color: rgb(51, 51, 51);">\</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token plain">  --header </span><span class="token string" style="color: rgb(3, 47, 98);">'Accept: application/json'</span><span class="token plain"> </span><span class="token punctuation" style="color: rgb(51, 51, 51);">\</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token plain">  --header </span><span class="token string" style="color: rgb(3, 47, 98);">'Content-Type: application/json'</span><span class="token plain"> </span><span class="token punctuation" style="color: rgb(51, 51, 51);">\</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token plain">  --header </span><span class="token string" style="color: rgb(3, 47, 98);">'Hiddify-API-Key: 123'</span><span class="token plain"> </span><span class="token punctuation" style="color: rgb(51, 51, 51);">\</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token plain">  --data </span><span class="token string" style="color: rgb(3, 47, 98);">'{</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "added_by_uuid": null,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "comment": null,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "current_usage_GB": 0,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "ed25519_private_key": "string",</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "ed25519_public_key": "string",</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "enable": true,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "is_active": true,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "lang": "en",</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "last_online": null,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "last_reset_time": null,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "mode": "no_reset",</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "name": "string",</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "package_days": 0,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "start_date": "string",</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "telegram_id": 0,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "usage_limit_GB": 0,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "uuid": null,</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "wg_pk": "string",</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "wg_psk": "string",</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">  "wg_pub": "string"</span></div></div><div class="sl-flex"><div class="sl-flex-1 sl-break-all"><span class="token string" style="color: rgb(3, 47, 98);">}'</span></div></div></div></div></pre></div></div></div><div class="sl-panel sl-outline-none sl-w-full sl-rounded-lg"><div class="sl-panel__titlebar sl-flex sl-items-center sl-relative focus:sl-z-10 sl-text-base sl-leading-none sl-pr-3 sl-pl-4 sl-bg-canvas-200 sl-text-body sl-border-input focus:sl-border-primary sl-select-none"><div class="sl-flex sl-flex-1 sl-items-center sl-h-lg"><span class="sl-text-body">Response Example</span></div><button type="button" class="sl-button sl-form-group-border sl-h-sm sl-text-base sl-font-medium sl-px-1.5 hover:sl-bg-canvas-50 active:sl-bg-canvas-100 sl-text-muted hover:sl-text-body focus:sl-text-body sl-rounded sl-border-transparent sl-border disabled:sl-opacity-70"><div class="sl-mx-0"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="copy" class="svg-inline--fa fa-copy fa-fw fa-sm sl-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M384 96L384 0h-112c-26.51 0-48 21.49-48 48v288c0 26.51 21.49 48 48 48H464c26.51 0 48-21.49 48-48V128h-95.1C398.4 128 384 113.6 384 96zM416 0v96h96L416 0zM192 352V128h-144c-26.51 0-48 21.49-48 48v288c0 26.51 21.49 48 48 48h192c26.51 0 48-21.49 48-48L288 416h-32C220.7 416 192 387.3 192 352z"></path></svg></div></button></div><div class="sl-panel__content-wrapper sl-bg-canvas-100"><div class="sl-panel__content sl-p-0"><pre tabindex="0" aria-label="{
  &quot;added_by_uuid&quot;: null,
  &quot;comment&quot;: null,
  &quot;current_usage_GB&quot;: 0,
  &quot;ed25519_private_key&quot;: &quot;string&quot;,
  &quot;ed25519_public_key&quot;: &quot;string&quot;,
  &quot;enable&quot;: true,
  &quot;id&quot;: 0,
  &quot;is_active&quot;: true,
  &quot;lang&quot;: &quot;en&quot;,
  &quot;last_online&quot;: null,
  &quot;last_reset_time&quot;: null,
  &quot;mode&quot;: &quot;no_reset&quot;,
  &quot;name&quot;: &quot;string&quot;,
  &quot;package_days&quot;: 0,
  &quot;start_date&quot;: &quot;string&quot;,
  &quot;telegram_id&quot;: 0,
  &quot;usage_limit_GB&quot;: 0,
  &quot;uuid&quot;: null,
  &quot;wg_pk&quot;: &quot;string&quot;,
  &quot;wg_psk&quot;: &quot;string&quot;,
  &quot;wg_pub&quot;: &quot;string&quot;
}" class="sl-code-viewer sl-grid sl-overflow-x-hidden sl-overflow-y-hidden sl-relative sl-outline-none sl-group" role="group" style="--fs-code: 12;"><div class="sl-code-viewer__scroller sl-overflow-x-auto sl-overflow-y-auto" style="max-height: 500px;"><div class="sl-code-highlight prism-code language-json" style="padding: 12px 15px; font-family: var(--font-code); font-size: var(--fs-code); line-height: var(--lh-code);"><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">1</div><div class="sl-flex-1 sl-break-all"><span class="token punctuation" style="color: rgb(51, 51, 51);">{</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">2</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"added_by_uuid"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(215, 58, 73);">null</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">3</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"comment"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(215, 58, 73);">null</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">4</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"current_usage_GB"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(0, 92, 197);">0</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">5</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"ed25519_private_key"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(3, 47, 98);">"string"</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">6</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"ed25519_public_key"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(3, 47, 98);">"string"</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">7</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"enable"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token boolean" style="color: rgb(0, 92, 197);">true</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">8</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"id"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(0, 92, 197);">0</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">9</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"is_active"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token boolean" style="color: rgb(0, 92, 197);">true</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">10</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"lang"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(3, 47, 98);">"en"</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">11</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"last_online"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(215, 58, 73);">null</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">12</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"last_reset_time"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(215, 58, 73);">null</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">13</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"mode"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(3, 47, 98);">"no_reset"</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">14</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"name"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(3, 47, 98);">"string"</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">15</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"package_days"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(0, 92, 197);">0</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">16</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"start_date"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(3, 47, 98);">"string"</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">17</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"telegram_id"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(0, 92, 197);">0</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">18</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"usage_limit_GB"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(0, 92, 197);">0</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">19</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"uuid"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(215, 58, 73);">null</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">20</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"wg_pk"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(3, 47, 98);">"string"</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">21</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"wg_psk"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(3, 47, 98);">"string"</span><span class="token punctuation" style="color: rgb(51, 51, 51);">,</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">22</div><div class="sl-flex-1 sl-break-all"><span class="token plain">  </span><span class="token property" style="color: rgb(24, 54, 145);">"wg_pub"</span><span class="token operator" style="color: rgb(215, 58, 73);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(3, 47, 98);">"string"</span><span class="token plain"></span></div></div><div class="sl-flex"><div class="sl-code-highlight__ln sl-flex-shrink-0 sl-select-none sl-opacity-50" style="width: 28px; font-size: 0.9em; padding-top: 0.1em; line-height: var(--lh-code);">23</div><div class="sl-flex-1 sl-break-all"><span class="token plain"></span><span class="token punctuation" style="color: rgb(51, 51, 51);">}</span></div></div></div></div></pre></div></div></div></div></div></div></div></div></div></div></div></div></div></div>


</elements-api><veepn-lock-screen><style>@font-face{font-family:FigtreeVF;src:url(chrome-extension://majdfhpaihoncoakbjgbdhglocklcgno/fonts/FigtreeVF.woff2) format("woff2 supports variations"),url(chrome-extension://majdfhpaihoncoakbjgbdhglocklcgno/fonts/FigtreeVF.woff2) format("woff2-variations");font-weight:100 1000;font-display:swap}</style></veepn-lock-screen><veepn-guard-alert><style>@font-face{font-family:FigtreeVF;src:url(chrome-extension://majdfhpaihoncoakbjgbdhglocklcgno/fonts/FigtreeVF.woff2) format("woff2 supports variations"),url(chrome-extension://majdfhpaihoncoakbjgbdhglocklcgno/fonts/FigtreeVF.woff2) format("woff2-variations");font-weight:100 1000;font-display:swap}</style></veepn-guard-alert></body>