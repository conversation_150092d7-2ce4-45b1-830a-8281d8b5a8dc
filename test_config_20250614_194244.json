{"dns": {"servers": [{"tag": "cloudflare", "address": "https://*******/dns-query", "address_resolver": "local", "detour": "proxy"}, {"tag": "local", "address": "*********", "detour": "direct"}], "rules": [{"outbound": "any", "server": "local"}], "final": "cloudflare", "strategy": "ipv4_only"}, "inbounds": [{"type": "tun", "inet4_address": "**********/30", "auto_route": true, "sniff": true}], "outbounds": [{"type": "selector", "tag": "proxy", "outbounds": ["trojan-ws", "vmess-ws", "trojan-grpc", "vmess-grpc"]}, {"type": "trojan", "tag": "trojan-ws", "server": "***********", "server_port": 443, "password": "15c175d8-703c-456a-ac82-91041f8af845", "tls": {"enabled": true, "server_name": "***********.sslip.io"}, "transport": {"type": "ws", "path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx", "headers": {"Host": "***********.sslip.io"}}}, {"type": "vmess", "tag": "vmess-ws", "server": "***********", "server_port": 443, "uuid": "15c175d8-703c-456a-ac82-91041f8af845", "security": "auto", "tls": {"enabled": true, "server_name": "***********.sslip.io"}, "transport": {"type": "ws", "path": "/39m0pgSOrY19tjCyr3egnx", "headers": {"Host": "***********.sslip.io"}}}, {"type": "trojan", "tag": "trojan-grpc", "server": "***********", "server_port": 443, "password": "15c175d8-703c-456a-ac82-91041f8af845", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "h2"}, "transport": {"type": "grpc", "service_name": "Cgm6B1DqLOKIFOh7gdS9"}}, {"type": "vmess", "tag": "vmess-grpc", "server": "***********", "server_port": 443, "uuid": "15c175d8-703c-456a-ac82-91041f8af845", "security": "auto", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "h2"}, "transport": {"type": "grpc", "service_name": "39m0pgSOOh7gdS9"}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}], "route": {"rules": [{"geoip": ["private"], "outbound": "direct"}], "final": "proxy", "auto_detect_interface": true}}