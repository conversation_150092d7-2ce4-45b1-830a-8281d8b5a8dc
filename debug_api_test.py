#!/usr/bin/env python3
"""
Отладочный скрипт для проверки конкретных проблем с API
"""

import requests
import json
import time

def test_duplicate_email():
    """Тестирует проблему с дублирующимся email"""
    print("🔍 Testing duplicate email issue...")
    
    # Создаем сессию
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Debug-Tester/1.0',
        'Content-Type': 'application/json'
    })
    
    api_url = "http://127.0.0.1:8001"
    
    # Первая регистрация
    user_data = {
        'email': '<EMAIL>',
        'password': 'DebugTest123!',
        'password_confirm': 'DebugTest123!',
        'device_id': 'debug-device-1',
        'device_name': 'Debug Device 1',
        'device_type': 'test'
    }
    
    print("1. First registration...")
    try:
        response1 = session.post(f"{api_url}/api/auth/register/", json=user_data, timeout=30)
        print(f"   Status: {response1.status_code}")
        print(f"   Response: {response1.text[:200]}...")
    except Exception as e:
        print(f"   Error: {e}")
        return False
    
    # Вторая регистрация с тем же email
    user_data['device_id'] = 'debug-device-2'
    user_data['device_name'] = 'Debug Device 2'
    
    print("2. Duplicate email registration...")
    try:
        response2 = session.post(f"{api_url}/api/auth/register/", json=user_data, timeout=30)
        print(f"   Status: {response2.status_code}")
        print(f"   Response: {response2.text}")
        
        if response2.status_code in [400, 409]:
            print("✅ Duplicate email correctly rejected")
            return True
        else:
            print("❌ Duplicate email was not rejected")
            return False
            
    except Exception as e:
        print(f"   Error: {e}")
        return False

def test_unauthorized_access():
    """Тестирует неавторизованный доступ"""
    print("\n🔍 Testing unauthorized access...")
    
    session = requests.Session()
    api_url = "http://127.0.0.1:8001"
    
    try:
        response = session.get(f"{api_url}/api/auth/profile/", timeout=30)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 401:
            print("✅ Unauthorized access correctly rejected")
            return True
        else:
            print("❌ Unauthorized access was not rejected")
            return False
            
    except Exception as e:
        print(f"   Error: {e}")
        return False

def test_invalid_refresh_token():
    """Тестирует невалидный refresh token"""
    print("\n🔍 Testing invalid refresh token...")
    
    session = requests.Session()
    session.headers.update({'Content-Type': 'application/json'})
    api_url = "http://127.0.0.1:8001"
    
    try:
        response = session.post(f"{api_url}/api/auth/token/refresh/", 
                              json={'refresh': 'invalid-token'}, 
                              timeout=30)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code in [400, 401]:
            print("✅ Invalid refresh token correctly rejected")
            return True
        else:
            print("❌ Invalid refresh token was not rejected")
            return False
            
    except Exception as e:
        print(f"   Error: {e}")
        return False

def test_mvp_api():
    """Тестирует MVP API (должен быть отключен)"""
    print("\n🔍 Testing MVP API (should be disabled)...")
    
    session = requests.Session()
    session.headers.update({'Content-Type': 'application/json'})
    api_url = "http://127.0.0.1:8001"
    
    try:
        response = session.post(f"{api_url}/api/v1/device/register/", 
                              json={'client_generated_device_id': 'test-device'}, 
                              timeout=30)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 404:
            print("✅ MVP API correctly disabled (404)")
            return True
        else:
            print("❌ MVP API is still active")
            return False
            
    except Exception as e:
        print(f"   Error: {e}")
        return False

def main():
    print("🐛 Debug API Test - Checking specific issues")
    print("=" * 50)
    
    results = []
    
    results.append(test_duplicate_email())
    results.append(test_unauthorized_access())
    results.append(test_invalid_refresh_token())
    results.append(test_mvp_api())
    
    print("\n📊 Results:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print("\n🎉 All tests passed! The API is working correctly.")
        print("The issue is likely in the main test script's request handling.")
    else:
        print("\n⚠️ Some tests failed. There may be actual API issues.")

if __name__ == '__main__':
    main()
