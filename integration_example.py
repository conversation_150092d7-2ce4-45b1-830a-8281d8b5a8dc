#!/usr/bin/env python3
"""
Пример интеграции системы персонализированных SingBox конфигураций 
с существующим VPN сервисом Django.

Демонстрирует, как использовать новую систему в реальных view функциях.
"""

import json
from typing import Dict, Optional
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

# Импорты из VPN сервиса (предполагаемые)
# from vpn.models import Location, HiddifyLink
# from accounts.models import UserAccount, ActiveSubscription
# from vpn.trojan_service import PersonalizedSingBoxService


class PersonalizedConfigView(View):
    """
    View для генерации персонализированных SingBox конфигураций.
    
    Заменяет старые методы генерации конфигураций на новую систему
    с поддержкой множественных протоколов и локаций.
    """
    
    @method_decorator(login_required)
    def get(self, request, location_id=None):
        """
        Генерирует персонализированную SingBox конфигурацию для пользователя.
        
        URL: /api/vpn/personalized-config/
        URL: /api/vpn/personalized-config/<location_id>/
        
        Query Parameters:
        - format: 'json' (default) | 'file' - формат ответа
        - download: 'true' | 'false' - скачать как файл
        """
        try:
            # Шаг 1: Получаем пользователя и проверяем подписку
            user_account = request.user.useraccount
            
            # Проверяем активную подписку
            active_subscription = ActiveSubscription.objects.filter(
                user=user_account,
                is_active=True,
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now()
            ).first()
            
            if not active_subscription:
                return JsonResponse({
                    'error': 'No active subscription found',
                    'code': 'NO_SUBSCRIPTION'
                }, status=403)
            
            # Шаг 2: Определяем локацию
            if location_id:
                try:
                    location = Location.objects.get(id=location_id, is_active=True)
                except Location.DoesNotExist:
                    return JsonResponse({
                        'error': 'Location not found or inactive',
                        'code': 'INVALID_LOCATION'
                    }, status=404)
            else:
                # Используем первую доступную локацию из подписки
                available_locations = active_subscription.plan.locations.filter(is_active=True)
                if not available_locations.exists():
                    return JsonResponse({
                        'error': 'No available locations for your subscription',
                        'code': 'NO_LOCATIONS'
                    }, status=404)
                location = available_locations.first()
            
            # Шаг 3: Получаем Hiddify UUID пользователя
            try:
                hiddify_link = HiddifyLink.objects.get(
                    user=user_account,
                    is_active_in_hiddify=True
                )
            except HiddifyLink.DoesNotExist:
                return JsonResponse({
                    'error': 'VPN access not configured. Please contact support.',
                    'code': 'NO_HIDDIFY_LINK'
                }, status=404)
            
            # Шаг 4: Генерируем персонализированную конфигурацию
            result = PersonalizedSingBoxService.get_config_for_existing_user(
                hiddify_user_uuid=str(hiddify_link.hiddify_user_uuid),
                location_params=location.hiddify_params,
                user_name=user_account.username or f"user_{user_account.id}"
            )
            
            if not result['success']:
                return JsonResponse({
                    'error': 'Failed to generate configuration',
                    'details': result.get('error', 'Unknown error'),
                    'code': 'CONFIG_GENERATION_FAILED'
                }, status=500)
            
            singbox_config = result['singbox_config']
            
            # Шаг 5: Формируем ответ
            response_format = request.GET.get('format', 'json')
            download = request.GET.get('download', 'false').lower() == 'true'
            
            if response_format == 'file' or download:
                # Возвращаем как файл для скачивания
                config_json = json.dumps(singbox_config, indent=2, ensure_ascii=False)
                filename = f"vpn_config_{user_account.username}_{location.country_code.lower()}.json"
                
                response = HttpResponse(
                    config_json,
                    content_type='application/json; charset=utf-8'
                )
                response['Content-Disposition'] = f'attachment; filename="{filename}"'
                return response
            else:
                # Возвращаем JSON ответ
                return JsonResponse({
                    'success': True,
                    'config': singbox_config,
                    'metadata': {
                        'user_id': user_account.id,
                        'location': {
                            'id': str(location.id),
                            'name': location.name,
                            'country_code': location.country_code,
                            'flag_emoji': location.flag_emoji
                        },
                        'subscription': {
                            'plan_name': active_subscription.plan.name,
                            'expires_at': active_subscription.end_date.isoformat()
                        },
                        'config_info': {
                            'format': 'singbox_json',
                            'protocols': ['trojan', 'vmess'],
                            'transports': ['websocket', 'grpc', 'httpupgrade'],
                            'generated_at': timezone.now().isoformat()
                        }
                    }
                })
                
        except Exception as e:
            logger.error(f"Error generating personalized config for user {request.user.id}: {str(e)}")
            return JsonResponse({
                'error': 'Internal server error',
                'code': 'INTERNAL_ERROR'
            }, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_user_with_config(request):
    """
    API endpoint для создания нового пользователя в Hiddify и генерации конфигурации.
    
    Используется при регистрации новых пользователей или активации подписок.
    
    POST /api/vpn/create-user-config/
    
    Body:
    {
        "plan_id": "uuid",
        "location_id": "uuid",
        "user_metadata": {
            "source": "web_registration",
            "plan_type": "premium"
        }
    }
    """
    try:
        user_account = request.user.useraccount
        data = request.data
        
        # Валидация входных данных
        plan_id = data.get('plan_id')
        location_id = data.get('location_id')
        user_metadata = data.get('user_metadata', {})
        
        if not plan_id or not location_id:
            return Response({
                'error': 'plan_id and location_id are required',
                'code': 'MISSING_PARAMETERS'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Получаем план и локацию
        try:
            subscription_plan = SubscriptionPlan.objects.get(id=plan_id, is_active=True)
            location = Location.objects.get(id=location_id, is_active=True)
        except (SubscriptionPlan.DoesNotExist, Location.DoesNotExist):
            return Response({
                'error': 'Invalid plan or location',
                'code': 'INVALID_PARAMETERS'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Проверяем, что локация доступна для плана
        if not subscription_plan.locations.filter(id=location_id).exists():
            return Response({
                'error': 'Location not available for selected plan',
                'code': 'LOCATION_NOT_AVAILABLE'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Подготавливаем данные для создания пользователя
        hiddify_user_name = f"{user_account.username}_{user_account.id}"
        usage_limit_gb = subscription_plan.traffic_limit_gb
        package_days = subscription_plan.duration_days
        
        comment_data = {
            'user_id': str(user_account.id),
            'username': user_account.username,
            'plan_id': str(subscription_plan.id),
            'plan_name': subscription_plan.name,
            'location_id': str(location.id),
            'location_name': location.name,
            'created_via': 'personalized_config_api',
            **user_metadata
        }
        
        # Создаем пользователя и генерируем конфигурацию
        result = PersonalizedSingBoxService.create_user_and_generate_config(
            user_name=hiddify_user_name,
            usage_limit_gb=usage_limit_gb,
            package_days=package_days,
            location_params=location.hiddify_params,
            comment_data=comment_data
        )
        
        if not result['success']:
            return Response({
                'error': 'Failed to create user and generate configuration',
                'details': result.get('error', 'Unknown error'),
                'code': 'USER_CREATION_FAILED'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Сохраняем связь с Hiddify в базе данных
        hiddify_link = HiddifyLink.objects.create(
            user=user_account,
            hiddify_user_uuid=result['hiddify_user_uuid'],
            is_active_in_hiddify=True,
            created_via='personalized_config_api'
        )
        
        # Создаем активную подписку
        active_subscription = ActiveSubscription.objects.create(
            user=user_account,
            plan=subscription_plan,
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=package_days),
            is_active=True
        )
        
        return Response({
            'success': True,
            'hiddify_user_uuid': result['hiddify_user_uuid'],
            'config': result['singbox_config'],
            'subscription': {
                'id': str(active_subscription.id),
                'plan_name': subscription_plan.name,
                'expires_at': active_subscription.end_date.isoformat(),
                'traffic_limit_gb': usage_limit_gb
            },
            'location': {
                'id': str(location.id),
                'name': location.name,
                'country_code': location.country_code,
                'flag_emoji': location.flag_emoji
            }
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        logger.error(f"Error creating user with config for user {request.user.id}: {str(e)}")
        return Response({
            'error': 'Internal server error',
            'code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_available_locations(request):
    """
    Возвращает список доступных локаций для пользователя.
    
    GET /api/vpn/locations/
    """
    try:
        user_account = request.user.useraccount
        
        # Получаем активную подписку
        active_subscription = ActiveSubscription.objects.filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()
        
        if not active_subscription:
            return Response({
                'error': 'No active subscription found',
                'locations': []
            }, status=status.HTTP_200_OK)
        
        # Получаем доступные локации
        available_locations = active_subscription.plan.locations.filter(is_active=True)
        
        locations_data = []
        for location in available_locations:
            # Получаем информацию о подключении для каждой локации
            connection_info = PersonalizedSingBoxService.get_connection_info(location.hiddify_params)
            
            locations_data.append({
                'id': str(location.id),
                'name': location.name,
                'country_code': location.country_code,
                'city': location.city,
                'flag_emoji': location.flag_emoji,
                'connection_info': connection_info
            })
        
        return Response({
            'success': True,
            'locations': locations_data,
            'total_count': len(locations_data)
        })
        
    except Exception as e:
        logger.error(f"Error getting locations for user {request.user.id}: {str(e)}")
        return Response({
            'error': 'Internal server error',
            'code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# URL patterns для интеграции (добавить в urls.py)
"""
from django.urls import path
from . import integration_example

urlpatterns = [
    # Персонализированные конфигурации
    path('vpn/personalized-config/', integration_example.PersonalizedConfigView.as_view(), name='personalized_config'),
    path('vpn/personalized-config/<uuid:location_id>/', integration_example.PersonalizedConfigView.as_view(), name='personalized_config_location'),
    
    # API endpoints
    path('api/vpn/create-user-config/', integration_example.create_user_with_config, name='create_user_config'),
    path('api/vpn/locations/', integration_example.get_available_locations, name='available_locations'),
]
"""
