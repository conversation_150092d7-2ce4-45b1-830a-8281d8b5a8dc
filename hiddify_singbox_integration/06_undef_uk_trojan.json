{"log": {"level": "info", "timestamp": true}, "dns": {"servers": [{"tag": "dns_proxy", "address": "https://*******/dns-query", "address_resolver": "dns_resolver", "strategy": "ipv4_only", "detour": "HLVPN_UK_UNDEF"}, {"tag": "dns_resolver", "address": "*******", "detour": "direct"}], "rules": [{"outbound": "any", "server": "dns_resolver"}]}, "inbounds": [{"type": "tun", "tag": "tun-in", "interface_name": "tun0", "inet4_address": "**********/30", "mtu": 9000, "auto_route": true, "strict_route": true, "endpoint_independent_nat": false, "stack": "system", "sniff": true, "sniff_override_destination": true}], "outbounds": [{"type": "trojan", "tag": "HLVPN_UK_UNDEF", "server": "undef-uk-1.undef.network", "server_port": 443, "password": "RcT0MxFOh9m431ovfzlKCKJnI", "tls": {"enabled": true, "server_name": "undef-uk-1.undef.network"}, "transport": {"type": "ws", "path": "/f2fc2a1f", "headers": {"Host": "undef-uk-1.undef.network"}}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns-out"}], "route": {"auto_detect_interface": true, "final": "HLVPN_UK_UNDEF", "rules": [{"protocol": "dns", "outbound": "dns-out"}, {"geoip": "private", "outbound": "direct"}]}}