{
  "log": {
    "level": "info",
    "timestamp": true
  },
  "dns": {
    "servers": [
      {
        "tag": "dns_proxy",
        "address": "https://*******/dns-query",
        "address_resolver": "dns_resolver",
        "strategy": "ipv4_only",
        "detour": "DUCTUSPRO_TROJAN"
      },
      {
        "tag": "dns_resolver",
        "address": "*******",
        "detour": "direct"
      }
    ],
    "rules": [
      {
        "outbound": "any",
        "server": "dns_resolver"
      }
    ]
  },
  "inbounds": [
    {
      "type": "tun",
      "tag": "tun-in",
      "interface_name": "tun0",
      "inet4_address": "**********/30",
      "mtu": 9000,
      "auto_route": true,
      "strict_route": true,
      "endpoint_independent_nat": false,
      "stack": "system",
      "sniff": true,
      "sniff_override_destination": true
    }
  ],
  "outbounds": [
    {
      "type": "trojan",
      "tag": "DUCTUSPRO_TROJAN",
      "server": "{{SERVER}}",
      "server_port": {{SERVER_PORT}},
      "password": "{{USER_PASSWORD}}",
      "tls": {
        "enabled": true,
        "server_name": "{{SERVER}}"
      },
      "transport": {
        "type": "ws",
        "path": "{{WS_PATH}}",
        "headers": {
          "Host": "{{SERVER}}"
        }
      }
    },
    {
      "type": "direct",
      "tag": "direct"
    },
    {
      "type": "block",
      "tag": "block"
    },
    {
      "type": "dns",
      "tag": "dns-out"
    }
  ],
  "route": {
    "auto_detect_interface": true,
    "final": "DUCTUSPRO_TROJAN",
    "rules": [
      {
        "protocol": "dns",
        "outbound": "dns-out"
      },
      {
        "geoip": "private",
        "outbound": "direct"
      }
    ]
  }
}
