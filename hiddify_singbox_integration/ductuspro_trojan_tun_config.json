{"log": {"level": "info", "timestamp": true}, "dns": {"servers": [{"tag": "dns_proxy", "address": "https://*******/dns-query", "address_resolver": "dns_resolver", "strategy": "ipv4_only", "detour": "DUCTUSPRO_TROJAN"}, {"tag": "dns_resolver", "address": "*******", "detour": "direct"}], "rules": [{"outbound": "any", "server": "dns_resolver"}]}, "inbounds": [{"type": "tun", "tag": "tun-in", "interface_name": "tun0", "inet4_address": "**********/30", "mtu": 9000, "auto_route": true, "strict_route": true, "endpoint_independent_nat": false, "stack": "system", "sniff": true, "sniff_override_destination": true}], "outbounds": [{"type": "trojan", "tag": "DUCTUSPRO_TROJAN", "server": "ductuspro.ru", "server_port": 443, "password": "0837472e-1048-4339-b824-0e059f5f8a1d", "tls": {"enabled": true, "server_name": "ductuspro.ru"}, "transport": {"type": "ws", "path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx", "headers": {"Host": "ductuspro.ru"}}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns-out"}], "route": {"auto_detect_interface": true, "final": "DUCTUSPRO_TROJAN", "rules": [{"protocol": "dns", "outbound": "dns-out"}, {"geoip": "private", "outbound": "direct"}]}}