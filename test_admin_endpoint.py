#!/usr/bin/env python3
"""
Тестовый скрипт для проверки административного endpoint генерации персонализированных SingBox конфигураций.
"""

import os
import sys
import django
import json
import requests
from datetime import datetime, timedelta

# Настройка Django
sys.path.insert(0, '/root/matrix')
sys.path.insert(0, '/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from accounts.models import UserAccount, UserDevice, HiddifyLink
from subscriptions.models import SubscriptionPlan, ActiveSubscription
from vpn.models import Location
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


def create_test_data():
    """Создает тестовые данные для проверки endpoint."""
    print("Создание тестовых данных...")
    
    # Создаем администратора
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'is_staff': True,
            'is_superuser': True,
            'is_active': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"✓ Создан администратор: {admin_user.email}")
    else:
        print(f"✓ Администратор уже существует: {admin_user.email}")
    
    # Создаем тестового пользователя
    test_user, created = UserAccount.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'is_anonymous': False,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Создан тестовый пользователь: {test_user.email}")
    else:
        print(f"✓ Тестовый пользователь уже существует: {test_user.email}")
    
    # Создаем тестовую локацию
    test_location, created = Location.objects.get_or_create(
        name='Test Netherlands',
        defaults={
            'country_code': 'NL',
            'city': 'Amsterdam',
            'flag_emoji': '🇳🇱',
            'is_active': True,
            'hiddify_params': {
                'server': '***********',
                'server_port': 443,
                'tls_server_name': '***********.sslip.io',
                'trojan_ws_path': '/Cgm6B1DqLOKIFrY19tjCyr3egnx',
                'vmess_ws_path': '/39m0pgSOrY19tjCyr3egnx',
                'vmess_httpupgrade_path': '/39m0pgSOdKbicJLIaR',
                'trojan_grpc_service': 'Cgm6B1DqLOKIFOh7gdS9',
                'vmess_grpc_service': '39m0pgSOOh7gdS9'
            }
        }
    )
    if created:
        print(f"✓ Создана тестовая локация: {test_location.name}")
    else:
        print(f"✓ Тестовая локация уже существует: {test_location.name}")
    
    # Создаем тестовый план подписки
    test_plan, created = SubscriptionPlan.objects.get_or_create(
        name='Test Premium Plan',
        defaults={
            'price': 9.99,
            'currency': 'EUR',
            'duration_days': 30,
            'traffic_limit_gb': 100,
            'max_devices': 5,
            'is_active': True
        }
    )
    if created:
        print(f"✓ Создан тестовый план: {test_plan.name}")
        # Добавляем локацию к плану
        test_plan.locations.add(test_location)
    else:
        print(f"✓ Тестовый план уже существует: {test_plan.name}")
        # Убеждаемся, что локация добавлена
        if not test_plan.locations.filter(id=test_location.id).exists():
            test_plan.locations.add(test_location)
    
    # Создаем активную подписку
    active_subscription, created = ActiveSubscription.objects.get_or_create(
        user=test_user,
        plan=test_plan,
        defaults={
            'start_date': timezone.now(),
            'end_date': timezone.now() + timedelta(days=30),
            'is_active': True
        }
    )
    if created:
        print(f"✓ Создана активная подписка для {test_user.email}")
    else:
        print(f"✓ Активная подписка уже существует для {test_user.email}")
    
    # Создаем Hiddify связь (опционально)
    hiddify_link, created = HiddifyLink.objects.get_or_create(
        user=test_user,
        defaults={
            'hiddify_user_uuid': test_user.id,  # Используем ID пользователя как UUID
            'is_active_in_hiddify': True
        }
    )
    if created:
        print(f"✓ Создана Hiddify связь для {test_user.email}")
    else:
        print(f"✓ Hiddify связь уже существует для {test_user.email}")
    
    return admin_user, test_user, test_location, test_plan


def get_admin_token(admin_user):
    """Получает JWT токен для администратора."""
    refresh = RefreshToken.for_user(admin_user)
    return str(refresh.access_token)


def test_admin_endpoint():
    """Тестирует административный endpoint."""
    print("\n" + "=" * 80)
    print("ТЕСТИРОВАНИЕ АДМИНИСТРАТИВНОГО ENDPOINT")
    print("=" * 80)
    
    # Создаем тестовые данные
    admin_user, test_user, test_location, test_plan = create_test_data()
    
    # Получаем токен администратора
    admin_token = get_admin_token(admin_user)
    print(f"\n✓ Получен JWT токен для администратора")
    
    # Настройки для запроса
    base_url = "http://localhost:8090"
    endpoint_url = f"{base_url}/api/vpn/admin/generate-personalized-config/"
    
    headers = {
        'Authorization': f'Bearer {admin_token}',
        'Content-Type': 'application/json'
    }
    
    # Тест 1: Успешная генерация конфигурации
    print(f"\nТЕСТ 1: Успешная генерация конфигурации")
    print(f"URL: {endpoint_url}")
    print(f"User ID: {test_user.id}")
    
    request_data = {
        'user_id': str(test_user.id),
        'force_recreate': False
    }
    
    try:
        response = requests.post(endpoint_url, json=request_data, headers=headers, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✓ Запрос выполнен успешно")
            
            # Проверяем структуру ответа
            if response_data.get('success'):
                print("✓ Статус success: True")
                
                # Проверяем наличие основных полей
                required_fields = ['user_info', 'config', 'metadata']
                for field in required_fields:
                    if field in response_data:
                        print(f"✓ Поле '{field}' присутствует")
                    else:
                        print(f"✗ Поле '{field}' отсутствует")
                
                # Проверяем структуру конфигурации
                config = response_data.get('config', {})
                config_sections = ['dns', 'inbounds', 'outbounds', 'route']
                for section in config_sections:
                    if section in config:
                        print(f"✓ Секция конфигурации '{section}' присутствует")
                    else:
                        print(f"✗ Секция конфигурации '{section}' отсутствует")
                
                # Проверяем outbound'ы
                outbounds = config.get('outbounds', [])
                if outbounds:
                    print(f"✓ Найдено {len(outbounds)} outbound'ов")
                    
                    # Проверяем наличие протокольных outbound'ов
                    protocol_outbounds = [ob for ob in outbounds if ob.get('type') in ['trojan', 'vmess']]
                    if protocol_outbounds:
                        print(f"✓ Найдено {len(protocol_outbounds)} протокольных outbound'ов")
                        
                        # Проверяем подстановку UUID
                        uuid_checks = []
                        for outbound in protocol_outbounds:
                            if outbound.get('type') == 'trojan':
                                uuid_correct = outbound.get('password') == str(test_user.id)
                                uuid_checks.append(uuid_correct)
                                print(f"  {'✓' if uuid_correct else '✗'} Trojan '{outbound.get('tag')}': UUID подстановка")
                            elif outbound.get('type') == 'vmess':
                                uuid_correct = outbound.get('uuid') == str(test_user.id)
                                uuid_checks.append(uuid_correct)
                                print(f"  {'✓' if uuid_correct else '✗'} VMess '{outbound.get('tag')}': UUID подстановка")
                        
                        if all(uuid_checks):
                            print("✓ Все UUID корректно подставлены")
                        else:
                            print("✗ Проблемы с подстановкой UUID")
                    else:
                        print("✗ Протокольные outbound'ы не найдены")
                else:
                    print("✗ Outbound'ы не найдены")
                
                # Сохраняем конфигурацию в файл
                config_filename = f"admin_generated_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(config_filename, 'w', encoding='utf-8') as f:
                    json.dump(response_data, f, indent=2, ensure_ascii=False)
                print(f"✓ Ответ сохранен в файл: {config_filename}")
                
            else:
                print(f"✗ Статус success: False")
                print(f"Ошибка: {response_data.get('error', 'Unknown error')}")
        else:
            print(f"✗ Ошибка HTTP: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Детали ошибки: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"Текст ошибки: {response.text}")
                
    except requests.exceptions.RequestException as e:
        print(f"✗ Ошибка запроса: {e}")
    except Exception as e:
        print(f"✗ Неожиданная ошибка: {e}")
    
    # Тест 2: Ошибка с несуществующим пользователем
    print(f"\nТЕСТ 2: Ошибка с несуществующим пользователем")
    
    fake_request_data = {
        'user_id': '00000000-0000-0000-0000-000000000000',
        'force_recreate': False
    }
    
    try:
        response = requests.post(endpoint_url, json=fake_request_data, headers=headers, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✓ Корректно возвращена ошибка 400 для несуществующего пользователя")
            response_data = response.json()
            if not response_data.get('success', True):
                print("✓ Статус success: False")
            if 'error' in response_data:
                print(f"✓ Сообщение об ошибке: {response_data['error']}")
        else:
            print(f"✗ Неожиданный статус код: {response.status_code}")
            
    except Exception as e:
        print(f"✗ Ошибка в тесте 2: {e}")
    
    # Тест 3: Ошибка авторизации (без токена)
    print(f"\nТЕСТ 3: Ошибка авторизации (без токена)")
    
    try:
        response = requests.post(endpoint_url, json=request_data, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 401:
            print("✓ Корректно возвращена ошибка 401 для неавторизованного запроса")
        else:
            print(f"✗ Неожиданный статус код: {response.status_code}")
            
    except Exception as e:
        print(f"✗ Ошибка в тесте 3: {e}")
    
    print("\n" + "=" * 80)
    print("ТЕСТИРОВАНИЕ ЗАВЕРШЕНО")
    print("=" * 80)


if __name__ == "__main__":
    print("Запуск тестирования административного endpoint...")
    test_admin_endpoint()
    print("\nТестирование завершено!")
