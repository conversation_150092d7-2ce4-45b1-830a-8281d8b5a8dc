#!/usr/bin/env python3
"""
Тестовый скрипт для проверки системы генерации персонализированных SingBox конфигураций.
Демонстрирует создание пользователей и генерацию конфигураций.
"""

import os
import sys
import django
import json
from datetime import datetime

# Настройка Django
sys.path.insert(0, '/root/matrix')
sys.path.insert(0, '/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

from vpn.trojan_service import PersonalizedSingBoxService


def test_personalized_config_generation():
    """Тестирует генерацию персонализированных конфигураций."""
    print("=" * 80)
    print("ТЕСТИРОВАНИЕ СИСТЕМЫ ПЕРСОНАЛИЗИРОВАННЫХ SINGBOX КОНФИГУРАЦИЙ")
    print("=" * 80)
    
    # Тестовые данные
    test_user_uuid = "15c175d8-703c-456a-ac82-91041f8af845"
    test_user_name = "test_user_001"
    
    # Параметры локации (на основе singbox_Config_example)
    location_params = {
        'server': '***********',
        'server_port': 443,
        'tls_server_name': '***********.sslip.io',
        'trojan_ws_path': '/Cgm6B1DqLOKIFrY19tjCyr3egnx',
        'vmess_ws_path': '/39m0pgSOrY19tjCyr3egnx',
        'vmess_httpupgrade_path': '/39m0pgSOdKbicJLIaR',
        'trojan_grpc_service': 'Cgm6B1DqLOKIFOh7gdS9',
        'vmess_grpc_service': '39m0pgSOOh7gdS9'
    }
    
    print(f"Тестовый пользователь: {test_user_name}")
    print(f"UUID пользователя: {test_user_uuid}")
    print(f"Сервер: {location_params['server']}")
    print("-" * 80)
    
    # Тест 1: Загрузка базового шаблона
    print("ТЕСТ 1: Загрузка базового шаблона")
    try:
        base_template = PersonalizedSingBoxService.load_base_template()
        print(f"✓ Базовый шаблон загружен успешно")
        print(f"  - DNS серверов: {len(base_template['dns']['servers'])}")
        print(f"  - Inbound'ов: {len(base_template['inbounds'])}")
        print(f"  - Outbound'ов: {len(base_template['outbounds'])}")
        print(f"  - Правил маршрутизации: {len(base_template['route']['rules'])}")
    except Exception as e:
        print(f"✗ Ошибка загрузки шаблона: {e}")
        return False
    
    print()
    
    # Тест 2: Генерация персонализированной конфигурации
    print("ТЕСТ 2: Генерация персонализированной конфигурации")
    try:
        personalized_config = PersonalizedSingBoxService.generate_personalized_config(
            hiddify_user_uuid=test_user_uuid,
            location_params=location_params,
            user_name=test_user_name
        )
        
        print(f"✓ Персонализированная конфигурация создана")
        
        # Проверяем outbound'ы
        outbounds = personalized_config['outbounds']
        protocol_outbounds = [ob for ob in outbounds if ob.get('type') in ['trojan', 'vmess']]
        
        print(f"  - Всего outbound'ов: {len(outbounds)}")
        print(f"  - Протокольных outbound'ов: {len(protocol_outbounds)}")
        
        # Проверяем, что UUID подставлен корректно
        for outbound in protocol_outbounds:
            if outbound.get('type') == 'trojan':
                if outbound.get('password') == test_user_uuid:
                    print(f"  ✓ Trojan outbound '{outbound['tag']}': UUID корректно подставлен")
                else:
                    print(f"  ✗ Trojan outbound '{outbound['tag']}': UUID не подставлен")
            elif outbound.get('type') == 'vmess':
                if outbound.get('uuid') == test_user_uuid:
                    print(f"  ✓ VMess outbound '{outbound['tag']}': UUID корректно подставлен")
                else:
                    print(f"  ✗ VMess outbound '{outbound['tag']}': UUID не подставлен")
        
        # Проверяем серверные параметры
        server_check = all(
            ob.get('server') == location_params['server'] 
            for ob in protocol_outbounds
        )
        if server_check:
            print(f"  ✓ Серверные параметры корректно подставлены")
        else:
            print(f"  ✗ Проблема с серверными параметрами")
            
    except Exception as e:
        print(f"✗ Ошибка генерации конфигурации: {e}")
        return False
    
    print()
    
    # Тест 3: Сохранение конфигурации в файл
    print("ТЕСТ 3: Сохранение конфигурации")
    try:
        config_filename = f"generated_config_{test_user_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(config_filename, 'w', encoding='utf-8') as f:
            json.dump(personalized_config, f, indent=2, ensure_ascii=False)
        
        print(f"✓ Конфигурация сохранена в файл: {config_filename}")
        print(f"  - Размер файла: {os.path.getsize(config_filename)} байт")
        
        # Проверяем валидность JSON
        with open(config_filename, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        print(f"✓ JSON валиден и может быть загружен обратно")
        
    except Exception as e:
        print(f"✗ Ошибка сохранения: {e}")
        return False
    
    print()
    
    # Тест 4: Тестирование метода для существующего пользователя
    print("ТЕСТ 4: Генерация конфигурации для существующего пользователя")
    try:
        result = PersonalizedSingBoxService.get_config_for_existing_user(
            hiddify_user_uuid=test_user_uuid,
            location_params=location_params,
            user_name=test_user_name
        )
        
        if result['success']:
            print(f"✓ Конфигурация для существующего пользователя создана")
            print(f"  - Информация о пользователе получена: {'user_info' in result}")
        else:
            print(f"✗ Ошибка: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"✗ Ошибка тестирования существующего пользователя: {e}")
    
    print()
    
    # Тест 5: Информация о подключении
    print("ТЕСТ 5: Получение информации о подключении")
    try:
        connection_info = PersonalizedSingBoxService.get_connection_info(location_params)
        print(f"✓ Информация о подключении получена")
        print(f"  - Поддерживаемые протоколы: {connection_info['protocols']}")
        print(f"  - Поддерживаемые транспорты: {connection_info['transports']}")
        print(f"  - Поддерживаемые клиенты: {connection_info['supported_clients']}")
        
    except Exception as e:
        print(f"✗ Ошибка получения информации: {e}")
    
    print()
    print("=" * 80)
    print("ТЕСТИРОВАНИЕ ЗАВЕРШЕНО")
    print("=" * 80)
    
    return True


def test_integration_with_hiddify():
    """Тестирует интеграцию с Hiddify Manager (требует рабочий API)."""
    print("\n" + "=" * 80)
    print("ТЕСТИРОВАНИЕ ИНТЕГРАЦИИ С HIDDIFY MANAGER")
    print("=" * 80)
    
    # Параметры тестового пользователя
    test_user_name = f"test_integration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    usage_limit_gb = 10
    package_days = 30
    
    location_params = {
        'server': '***********',
        'server_port': 443,
        'tls_server_name': '***********.sslip.io',
        'trojan_ws_path': '/Cgm6B1DqLOKIFrY19tjCyr3egnx',
        'vmess_ws_path': '/39m0pgSOrY19tjCyr3egnx',
        'vmess_httpupgrade_path': '/39m0pgSOdKbicJLIaR',
        'trojan_grpc_service': 'Cgm6B1DqLOKIFOh7gdS9',
        'vmess_grpc_service': '39m0pgSOOh7gdS9'
    }
    
    comment_data = {
        'test_run': True,
        'created_by': 'test_personalized_singbox.py',
        'timestamp': datetime.now().isoformat()
    }
    
    print(f"Создание тестового пользователя: {test_user_name}")
    print(f"Лимит трафика: {usage_limit_gb} ГБ")
    print(f"Срок действия: {package_days} дней")
    print("-" * 80)
    
    try:
        result = PersonalizedSingBoxService.create_user_and_generate_config(
            user_name=test_user_name,
            usage_limit_gb=usage_limit_gb,
            package_days=package_days,
            location_params=location_params,
            comment_data=comment_data
        )
        
        if result['success']:
            print(f"✓ Пользователь создан и конфигурация сгенерирована")
            print(f"  - Hiddify UUID: {result['hiddify_user_uuid']}")
            print(f"  - Конфигурация содержит {len(result['singbox_config']['outbounds'])} outbound'ов")
            
            # Сохраняем конфигурацию
            config_filename = f"hiddify_integration_{test_user_name}.json"
            with open(config_filename, 'w', encoding='utf-8') as f:
                json.dump(result['singbox_config'], f, indent=2, ensure_ascii=False)
            
            print(f"✓ Конфигурация сохранена: {config_filename}")
            
        else:
            print(f"✗ Ошибка создания пользователя: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"✗ Ошибка интеграции с Hiddify: {e}")
    
    print("=" * 80)


if __name__ == "__main__":
    print("Запуск тестирования системы персонализированных SingBox конфигураций...")
    
    # Основные тесты
    success = test_personalized_config_generation()
    
    if success:
        # Тесты интеграции (только если основные тесты прошли)
        try:
            test_integration_with_hiddify()
        except Exception as e:
            print(f"Тесты интеграции пропущены из-за ошибки: {e}")
    
    print("\nТестирование завершено!")
