#!/usr/bin/env python3
"""
Скрипт для обновления локации в базе данных - изменение сервера с домена на IP адрес
"""
import os
import sys
import django

# Настройка Django
sys.path.append('/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

from vpn.models import Location

def update_location_server():
    """Обновляет сервер в локации с ductuspro.ru на ***********"""
    
    print("=== ОБНОВЛЕНИЕ ЛОКАЦИИ ===")
    
    try:
        # Ищем локацию с ductuspro.ru
        locations = Location.objects.filter(
            hiddify_params__server='ductuspro.ru'
        )
        
        if not locations.exists():
            print("❌ Локация с сервером 'ductuspro.ru' не найдена")
            
            # Показываем все локации
            all_locations = Location.objects.all()
            print(f"Найдено {all_locations.count()} локаций:")
            for loc in all_locations:
                server = loc.hiddify_params.get('server', 'unknown')
                print(f"  - {loc.name}: {server}")
            
            return False
        
        print(f"Найдено {locations.count()} локаций с сервером 'ductuspro.ru'")
        
        for location in locations:
            print(f"\nОбновляем локацию: {location.name}")
            print(f"Старый сервер: {location.hiddify_params.get('server')}")
            
            # Обновляем параметры
            old_params = location.hiddify_params.copy()
            new_params = old_params.copy()
            
            # Меняем сервер
            new_params['server'] = '***********'
            
            # Меняем tls_server_name если он есть
            if 'tls_server_name' in new_params:
                old_tls_name = new_params['tls_server_name']
                new_params['tls_server_name'] = '***********.sslip.io'
                print(f"Старый TLS server name: {old_tls_name}")
                print(f"Новый TLS server name: {new_params['tls_server_name']}")
            
            # Обновляем Host в WebSocket headers если есть
            if 'trojan_ws_headers' in new_params and 'Host' in new_params['trojan_ws_headers']:
                new_params['trojan_ws_headers']['Host'] = '***********.sslip.io'
            
            if 'vmess_ws_headers' in new_params and 'Host' in new_params['vmess_ws_headers']:
                new_params['vmess_ws_headers']['Host'] = '***********.sslip.io'
            
            # Сохраняем изменения
            location.hiddify_params = new_params
            location.save()
            
            print(f"✅ Локация {location.name} обновлена")
            print(f"Новый сервер: {location.hiddify_params.get('server')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при обновлении локации: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_location_if_needed():
    """Создает тестовую локацию с правильными параметрами если её нет"""
    
    print("\n=== СОЗДАНИЕ ТЕСТОВОЙ ЛОКАЦИИ ===")
    
    try:
        # Проверяем, есть ли локация с IP адресом
        ip_locations = Location.objects.filter(
            hiddify_params__server='***********'
        )
        
        if ip_locations.exists():
            print(f"✅ Найдена локация с IP адресом: {ip_locations.first().name}")
            return True
        
        # Создаем новую локацию с правильными параметрами
        test_location = Location.objects.create(
            name='Test Netherlands IP',
            country_code='NL',
            city='Amsterdam',
            flag_emoji='🇳🇱',
            is_active=True,
            hiddify_params={
                'server': '***********',
                'server_port': 443,
                'tls_server_name': '***********.sslip.io',
                'trojan_ws_path': '/Cgm6B1DqLOKIFrY19tjCyr3egnx',
                'vmess_ws_path': '/39m0pgSOrY19tjCyr3egnx',
                'vmess_httpupgrade_path': '/39m0pgSOdKbicJLIaR',
                'trojan_grpc_service': 'Cgm6B1DqLOKIFOh7gdS9',
                'vmess_grpc_service': '39m0pgSOOh7gdS9'
            }
        )
        
        print(f"✅ Создана тестовая локация: {test_location.name}")
        print(f"Сервер: {test_location.hiddify_params.get('server')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при создании тестовой локации: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_current_locations():
    """Показывает текущие локации"""
    
    print("\n=== ТЕКУЩИЕ ЛОКАЦИИ ===")
    
    try:
        locations = Location.objects.all()
        
        if not locations.exists():
            print("❌ Локации не найдены")
            return
        
        for location in locations:
            server = location.hiddify_params.get('server', 'unknown')
            tls_name = location.hiddify_params.get('tls_server_name', 'unknown')
            print(f"📍 {location.name}")
            print(f"   Server: {server}")
            print(f"   TLS Server Name: {tls_name}")
            print(f"   Active: {location.is_active}")
            print()
        
    except Exception as e:
        print(f"❌ Ошибка при получении локаций: {e}")

if __name__ == "__main__":
    print("Скрипт обновления локации для соответствия эталонному примеру")
    print("=" * 60)
    
    # Показываем текущие локации
    show_current_locations()
    
    # Обновляем существующие локации
    update_success = update_location_server()
    
    # Создаем тестовую локацию если нужно
    create_success = create_test_location_if_needed()
    
    # Показываем результат
    show_current_locations()
    
    if update_success or create_success:
        print("✅ Локации обновлены успешно!")
    else:
        print("❌ Не удалось обновить локации")
