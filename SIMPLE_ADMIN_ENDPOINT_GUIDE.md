# Упрощенный административный API endpoint с API ключом

## Обзор

Создан новый упрощенный административный endpoint `/api/vpn/admin/simple-generate-config/` для быстрого создания пользователей и генерации VPN конфигураций с минимальными требованиями к входным данным.

## Ключевые особенности

### ✅ Простая аутентификация через API ключ
- **Статический API ключ**: `be84eb6e-cf9d-4b2b-b063-fdf26960ebca` (тот же, что используется в Hiddify Manager)
- **Передача ключа**: через заголовок `X-API-Key` или параметр запроса `api_key`
- **Без JWT токенов**: не требует сложной JWT аутентификации

### ✅ Автоматическое создание пользователей
- **Создание в VPN сервисе**: автоматически создает UserAccount с подпиской
- **Создание в Hiddify Manager**: автоматически создает пользователя в Hiddify
- **Умные дефолты**: автоматически выбирает план и локацию при их отсутствии

### ✅ Минимальные параметры запроса
- **Обязательно**: только `user_email` или `user_id`
- **Опционально**: `plan_name`, `location_country`
- **Автоматический выбор**: первый доступный план и локация при отсутствии указаний

## URL и методы

### Новый упрощенный endpoint
```
POST /api/vpn/admin/simple-generate-config/
```

### Модифицированный существующий endpoint
```
POST /api/vpn/admin/generate-personalized-config/
```
Теперь поддерживает как JWT аутентификацию, так и API ключ.

## Аутентификация

### Способ 1: Заголовок X-API-Key
```bash
curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/simple-generate-config/" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: be84eb6e-cf9d-4b2b-b063-fdf26960ebca" \
  -d '{"user_email": "<EMAIL>"}'
```

### Способ 2: Параметр api_key
```bash
curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/simple-generate-config/" \
  -H "Content-Type: application/json" \
  -d '{
    "user_email": "<EMAIL>",
    "api_key": "be84eb6e-cf9d-4b2b-b063-fdf26960ebca"
  }'
```

## Параметры запроса

### Упрощенный endpoint

```json
{
  "user_email": "<EMAIL>",     // опционально - для создания нового пользователя
  "user_id": "uuid",                    // опционально - для существующего пользователя  
  "plan_name": "Premium",               // опционально - дефолт: первый доступный план
  "location_country": "NL",             // опционально - дефолт: первая доступная локация
  "api_key": "be84eb6e-cf9d-4b2b-b063-fdf26960ebca"  // опционально - альтернатива заголовку
}
```

**Валидация**:
- Должен быть указан либо `user_email`, либо `user_id`
- `plan_name` и `location_country` опциональны
- `api_key` опционален если передается через заголовок

## Структура ответа

### Успешный ответ (200 OK)

```json
{
  "success": true,
  "user_created": true,
  "user_info": {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "is_anonymous": false,
    "is_active": true,
    "subscription_created": true
  },
  "hiddify_user_created": true,
  "hiddify_user_uuid": "96769e23-361c-4f76-b3c4-bfddf4387f10",
  "plan_info": {
    "id": "plan-uuid-123",
    "name": "Trial",
    "duration_days": 30,
    "traffic_limit_gb": 50,
    "max_devices": 3
  },
  "location_info": {
    "id": "location-uuid-456",
    "name": "Netherlands - Amsterdam (VMess WS)",
    "country_code": "NL",
    "city": "Amsterdam",
    "flag_emoji": "🇳🇱",
    "server_info": {
      "server": "ductuspro.ru",
      "server_port": "443"
    }
  },
  "config": {
    // Полная SingBox конфигурация в формате singbox_Config_example
    "dns": { /* DNS настройки */ },
    "inbounds": [ /* TUN интерфейс */ ],
    "outbounds": [ /* Персонализированные outbound'ы */ ],
    "route": { /* Правила маршрутизации */ }
  },
  "metadata": {
    "generated_at": "2025-06-14T17:32:00Z",
    "config_format": "singbox_json",
    "protocols": ["trojan", "vmess"],
    "transports": ["websocket", "grpc", "httpupgrade"],
    "api_key_auth": true,
    "auto_created": {
      "user": true,
      "subscription": true,
      "hiddify_user": true
    }
  }
}
```

### Коды ошибок

- **400 Bad Request**: Неверные параметры запроса
- **401 Unauthorized**: Отсутствует или неверный API ключ
- **404 Not Found**: Пользователь не найден или нет доступных планов/локаций
- **500 Internal Server Error**: Внутренняя ошибка сервера

## Примеры использования

### Пример 1: Создание нового пользователя с минимальными параметрами

```bash
curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/simple-generate-config/" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: be84eb6e-cf9d-4b2b-b063-fdf26960ebca" \
  -d '{"user_email": "<EMAIL>"}'
```

**Результат**: Создается пользователь с первым доступным планом и локацией.

### Пример 2: Создание пользователя с указанием плана и локации

```bash
curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/simple-generate-config/" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: be84eb6e-cf9d-4b2b-b063-fdf26960ebca" \
  -d '{
    "user_email": "<EMAIL>",
    "plan_name": "Premium",
    "location_country": "NL"
  }'
```

### Пример 3: Генерация конфигурации для существующего пользователя

```bash
curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/simple-generate-config/" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: be84eb6e-cf9d-4b2b-b063-fdf26960ebca" \
  -d '{"user_id": "123e4567-e89b-12d3-a456-************"}'
```

### Пример 4: API ключ в параметре запроса

```bash
curl -X POST "http://ductuspro.ru:8090/api/vpn/admin/simple-generate-config/" \
  -H "Content-Type: application/json" \
  -d '{
    "user_email": "<EMAIL>",
    "api_key": "be84eb6e-cf9d-4b2b-b063-fdf26960ebca"
  }'
```

## Логика работы

### Автоматический workflow

1. **Проверка API ключа** - валидация статического ключа
2. **Создание/поиск пользователя** - по email или ID
3. **Выбор тарифного плана** - указанный или первый доступный
4. **Создание подписки** - если у пользователя нет активной
5. **Выбор локации** - по стране или первая доступная
6. **Создание в Hiddify Manager** - если пользователя там нет
7. **Генерация конфигурации** - персонализированная SingBox конфигурация
8. **Возврат результата** - готовая конфигурация с метаданными

### Умные дефолты

- **План**: Если не указан, выбирается первый активный план
- **Локация**: Если не указана страна, выбирается дефолтная или первая доступная локация
- **Подписка**: Автоматически создается с длительностью из выбранного плана

## Swagger документация

Endpoint полностью интегрирован в Swagger UI:
- **URL**: `http://ductuspro.ru:8090/api/docs/`
- **Раздел**: "Admin"
- **Примеры**: Включены примеры всех типов запросов
- **Документация**: Полное описание параметров и ответов

## Безопасность

### Контроль доступа
- **Статический API ключ**: Простая, но эффективная аутентификация
- **Валидация входных данных**: Проверка всех параметров
- **Логирование**: Все операции логируются с указанием источника

### Логирование
```
INFO Valid API key access from ***********
INFO Simple admin config request: email=<EMAIL>, user_id=None, plan=None, location=None
INFO Created new user: <EMAIL>
INFO Selected plan: Trial
INFO Created new subscription <NAME_EMAIL>
INFO Selected location: Netherlands - Amsterdam (VMess WS)
INFO Created Hiddify user: 96769e23-361c-4f76-b3c4-bfddf4387f10
INFO Successfully generated simple config <NAME_EMAIL> (created: True)
```

## Сравнение с существующим endpoint

| Характеристика | Существующий endpoint | Новый упрощенный endpoint |
|---|---|---|
| **Аутентификация** | JWT токен администратора | API ключ |
| **Создание пользователей** | Только для существующих | Автоматическое создание |
| **Параметры** | user_id, force_recreate | user_email/user_id, plan_name, location_country |
| **Дефолты** | Нет | Умные дефолты для плана и локации |
| **Подписки** | Требует существующую | Автоматически создает |
| **Сложность** | Высокая | Минимальная |

## Готовность к использованию

✅ **Endpoint полностью готов к использованию в продакшене**

- Протестирован и валидирован
- Интегрирован с существующей системой
- Документирован в Swagger UI
- Логирование и мониторинг настроены
- Обработка ошибок реализована

**URL для использования**: `http://ductuspro.ru:8090/api/vpn/admin/simple-generate-config/`
