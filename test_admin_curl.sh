#!/bin/bash

# Тестовый скрипт для проверки административного endpoint с помощью curl

echo "=========================================="
echo "ТЕСТИРОВАНИЕ АДМИНИСТРАТИВНОГО ENDPOINT"
echo "=========================================="

BASE_URL="http://localhost:8090"
ENDPOINT_URL="${BASE_URL}/api/vpn/admin/generate-personalized-config/"

echo "URL: ${ENDPOINT_URL}"
echo ""

# Тест 1: Проверка без авторизации (должен вернуть 401)
echo "ТЕСТ 1: Запрос без авторизации"
echo "Ожидается: HTTP 401 Unauthorized"
echo ""

curl -X POST "${ENDPOINT_URL}" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "123e4567-e89b-12d3-a456-426614174000"}' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo ""
echo "=========================================="

# Тест 2: Проверка с неверным токеном (должен вернуть 401)
echo "ТЕСТ 2: Запрос с неверным токеном"
echo "Ожидается: HTTP 401 Unauthorized"
echo ""

curl -X POST "${ENDPOINT_URL}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer invalid_token_123" \
  -d '{"user_id": "123e4567-e89b-12d3-a456-426614174000"}' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo ""
echo "=========================================="

# Тест 3: Проверка структуры endpoint (OPTIONS запрос)
echo "ТЕСТ 3: Проверка доступных методов"
echo "Ожидается: Allow: POST, OPTIONS"
echo ""

curl -X OPTIONS "${ENDPOINT_URL}" \
  -w "\nHTTP Status: %{http_code}\n" \
  -v 2>&1 | grep -E "(Allow:|HTTP/)"

echo ""
echo "=========================================="

# Тест 4: Проверка Swagger документации
echo "ТЕСТ 4: Проверка Swagger документации"
echo "URL: ${BASE_URL}/api/docs/"
echo ""

SWAGGER_RESPONSE=$(curl -s "${BASE_URL}/api/docs/" | grep -o "generate-personalized-config" | head -1)

if [ "$SWAGGER_RESPONSE" = "generate-personalized-config" ]; then
    echo "✓ Endpoint найден в Swagger документации"
else
    echo "✗ Endpoint не найден в Swagger документации"
fi

echo ""
echo "=========================================="

# Тест 5: Проверка схемы API
echo "ТЕСТ 5: Проверка схемы API"
echo "URL: ${BASE_URL}/api/schema/"
echo ""

SCHEMA_RESPONSE=$(curl -s "${BASE_URL}/api/schema/" | grep -o "generate-personalized-config" | head -1)

if [ "$SCHEMA_RESPONSE" = "generate-personalized-config" ]; then
    echo "✓ Endpoint найден в API схеме"
else
    echo "✗ Endpoint не найден в API схеме"
fi

echo ""
echo "=========================================="
echo "ТЕСТИРОВАНИЕ ЗАВЕРШЕНО"
echo "=========================================="

# Дополнительная информация
echo ""
echo "ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ:"
echo "- Для полного тестирования требуется JWT токен администратора"
echo "- Создайте администратора через Django admin или shell"
echo "- Получите JWT токен через /api/auth/login/"
echo "- Используйте токен в заголовке: Authorization: Bearer <token>"
echo ""
echo "Пример полного запроса:"
echo "curl -X POST '${ENDPOINT_URL}' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer <admin_jwt_token>' \\"
echo "  -d '{\"user_id\": \"<user_uuid>\", \"force_recreate\": false}'"
