{"success": true, "user_info": {"id": "295b8372-4988-4058-addb-945a855d8845", "email": "<EMAIL>", "is_anonymous": false, "is_active": true, "date_joined": "2025-06-01T11:25:05.755000Z", "last_login": "2025-06-01T11:29:09.515000Z", "active_subscription": {"plan_name": "Test Premium Plan", "expires_at": "2025-07-14T16:44:48.984216+00:00", "traffic_limit_gb": 100, "max_devices": 5}, "device_count": 1}, "hiddify_user_uuid": "5b7414cf-b91b-40bc-b65d-ffcc017f4255", "location_info": {"id": "1f56e4d4-02c2-40a4-9c6f-a28e9ed2b360", "name": "Test Netherlands", "country_code": "NL", "city": "Amsterdam", "flag_emoji": "🇳🇱", "is_active": true, "server_info": {"server": "***********", "server_port": "443"}}, "config": {"dns": {"servers": [{"tag": "cloudflare", "address": "https://*******/dns-query", "address_resolver": "local", "detour": "proxy"}, {"tag": "cloudflare-tls", "address": "tls://*******", "address_resolver": "local", "detour": "proxy"}, {"tag": "google", "address": "tls://*******", "address_resolver": "local", "detour": "proxy"}, {"tag": "local", "address": "*********", "detour": "direct"}, {"tag": "block", "address": "rcode://success"}], "rules": [{"outbound": "any", "server": "local"}, {"geoip": ["private"], "server": "local"}, {"geoip": ["cn"], "server": "local"}, {"domain_suffix": [".cn", ".ru"], "server": "local"}], "final": "cloudflare", "strategy": "ipv4_only", "disable_cache": false, "disable_expire": false}, "inbounds": [{"type": "tun", "inet4_address": "**********/30", "inet6_address": "fdfe:dcba:9876::1/126", "auto_route": true, "strict_route": false, "sniff": true, "sniff_override_destination": false, "domain_strategy": "ipv4_only"}], "outbounds": [{"type": "selector", "tag": "proxy", "outbounds": ["trojan-ws", "vmess-ws", "vmess-httpupgrade", "trojan-grpc", "vmess-grpc"]}, {"type": "trojan", "tag": "trojan-ws", "server": "***********", "server_port": 443, "password": "5b7414cf-b91b-40bc-b65d-ffcc017f4255", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "http/1.1", "utls": {"enabled": true, "fingerprint": "chrome"}}, "transport": {"type": "ws", "path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx", "headers": {"Host": "***********.sslip.io"}, "early_data_header_name": "Sec-WebSocket-Protocol"}}, {"type": "vmess", "tag": "vmess-ws", "server": "***********", "server_port": 443, "uuid": "5b7414cf-b91b-40bc-b65d-ffcc017f4255", "security": "auto", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "http/1.1", "utls": {"enabled": true, "fingerprint": "chrome"}}, "packet_encoding": "xudp", "transport": {"type": "ws", "path": "/39m0pgSOrY19tjCyr3egnx", "headers": {"Host": "***********.sslip.io"}, "early_data_header_name": "Sec-WebSocket-Protocol"}}, {"type": "vmess", "tag": "vmess-httpupgrade", "server": "***********", "server_port": 443, "uuid": "5b7414cf-b91b-40bc-b65d-ffcc017f4255", "security": "auto", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "http/1.1", "utls": {"enabled": true, "fingerprint": "chrome"}}, "packet_encoding": "xudp", "transport": {"type": "httpupgrade", "path": "/39m0pgSOdKbicJLIaR", "headers": {"Host": "***********.sslip.io"}}}, {"type": "trojan", "tag": "trojan-grpc", "server": "***********", "server_port": 443, "password": "5b7414cf-b91b-40bc-b65d-ffcc017f4255", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "h2", "utls": {"enabled": true, "fingerprint": "chrome"}}, "transport": {"type": "grpc", "service_name": "Cgm6B1DqLOKIFOh7gdS9", "idle_timeout": "1m55s", "ping_timeout": "15s"}}, {"type": "vmess", "tag": "vmess-grpc", "server": "***********", "server_port": 443, "uuid": "5b7414cf-b91b-40bc-b65d-ffcc017f4255", "security": "auto", "tls": {"enabled": true, "server_name": "***********.sslip.io", "alpn": "h2", "utls": {"enabled": true, "fingerprint": "chrome"}}, "packet_encoding": "xudp", "transport": {"type": "grpc", "service_name": "39m0pgSOOh7gdS9", "idle_timeout": "1m55s", "ping_timeout": "15s"}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns-out"}], "route": {"rules": [{"protocol": "dns", "outbound": "dns-out"}, {"geoip": ["private"], "outbound": "direct"}, {"geoip": ["cn"], "outbound": "direct"}, {"domain_suffix": [".cn", ".ru"], "outbound": "direct"}], "final": "proxy", "auto_detect_interface": true}}, "metadata": {"generated_at": "2025-06-14T16:44:49.561699+00:00", "generated_by_admin": "<EMAIL>", "config_format": "singbox_json", "protocols": ["trojan", "vmess"], "transports": ["websocket", "grpc", "httpupgrade"], "admin_generated": true, "force_recreate": false}}